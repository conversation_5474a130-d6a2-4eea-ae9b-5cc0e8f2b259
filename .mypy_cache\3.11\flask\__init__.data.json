{".class": "MypyFile", "_fullname": "flask", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Blueprint": {".class": "SymbolTableNode", "cross_ref": "flask.blueprints.Blueprint", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "flask.config.Config", "kind": "Gdef"}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "Request": {".class": "SymbolTableNode", "cross_ref": "flask.wrappers.Request", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "flask.wrappers.Response", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.__file__", "name": "__file__", "type": "builtins.str"}}, "__getattr__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "abort": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.abort", "kind": "Gdef"}, "after_this_request": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.after_this_request", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "appcontext_popped": {".class": "SymbolTableNode", "cross_ref": "flask.signals.appcontext_popped", "kind": "Gdef"}, "appcontext_pushed": {".class": "SymbolTableNode", "cross_ref": "flask.signals.appcontext_pushed", "kind": "Gdef"}, "appcontext_tearing_down": {".class": "SymbolTableNode", "cross_ref": "flask.signals.appcontext_tearing_down", "kind": "Gdef"}, "before_render_template": {".class": "SymbolTableNode", "cross_ref": "flask.signals.before_render_template", "kind": "Gdef"}, "copy_current_request_context": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.copy_current_request_context", "kind": "Gdef"}, "current_app": {".class": "SymbolTableNode", "cross_ref": "flask.globals.current_app", "kind": "Gdef"}, "flash": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.flash", "kind": "Gdef"}, "g": {".class": "SymbolTableNode", "cross_ref": "flask.globals.g", "kind": "Gdef"}, "get_flashed_messages": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.get_flashed_messages", "kind": "Gdef"}, "get_template_attribute": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.get_template_attribute", "kind": "Gdef"}, "got_request_exception": {".class": "SymbolTableNode", "cross_ref": "flask.signals.got_request_exception", "kind": "Gdef"}, "has_app_context": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.has_app_context", "kind": "Gdef"}, "has_request_context": {".class": "SymbolTableNode", "cross_ref": "flask.ctx.has_request_context", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "flask.json", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "make_response": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.make_response", "kind": "Gdef"}, "message_flashed": {".class": "SymbolTableNode", "cross_ref": "flask.signals.message_flashed", "kind": "Gdef"}, "redirect": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.redirect", "kind": "Gdef"}, "render_template": {".class": "SymbolTableNode", "cross_ref": "flask.templating.render_template", "kind": "Gdef"}, "render_template_string": {".class": "SymbolTableNode", "cross_ref": "flask.templating.render_template_string", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "request_finished": {".class": "SymbolTableNode", "cross_ref": "flask.signals.request_finished", "kind": "Gdef"}, "request_started": {".class": "SymbolTableNode", "cross_ref": "flask.signals.request_started", "kind": "Gdef"}, "request_tearing_down": {".class": "SymbolTableNode", "cross_ref": "flask.signals.request_tearing_down", "kind": "Gdef"}, "send_file": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.send_file", "kind": "Gdef"}, "send_from_directory": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.send_from_directory", "kind": "Gdef"}, "session": {".class": "SymbolTableNode", "cross_ref": "flask.globals.session", "kind": "Gdef"}, "stream_template": {".class": "SymbolTableNode", "cross_ref": "flask.templating.stream_template", "kind": "Gdef"}, "stream_template_string": {".class": "SymbolTableNode", "cross_ref": "flask.templating.stream_template_string", "kind": "Gdef"}, "stream_with_context": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.stream_with_context", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "template_rendered": {".class": "SymbolTableNode", "cross_ref": "flask.signals.template_rendered", "kind": "Gdef"}, "url_for": {".class": "SymbolTableNode", "cross_ref": "flask.helpers.url_for", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\__init__.py"}
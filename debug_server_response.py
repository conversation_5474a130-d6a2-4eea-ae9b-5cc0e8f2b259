#!/usr/bin/env python3
"""
Debug the raw server response to understand chunk counting.
"""

import requests
import json
import sys

def debug_server_response():
    """Debug what the server actually returns"""
    print("🔍 Debugging Raw Server Response")
    print("=" * 50)
    
    server_url = "http://home-ai-server.local:5002"
    
    # Test the exact same query
    payload = {
        "query": "how is memory managed in the utils codebase?",
        "codebase_name": "utils",
        "n_results": 15,
        "context_preferences": None
    }
    
    print("1. Testing /tools/get_optimized_context endpoint...")
    print(f"   Payload: {payload}")
    
    try:
        response = requests.post(
            f"{server_url}/tools/get_optimized_context",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   Response keys: {list(data.keys())}")
            
            # Check for chunk count fields
            chunk_fields = ['chunk_count', 'chunks', 'results_count', 'num_results', 'count']
            for field in chunk_fields:
                if field in data:
                    print(f"   {field}: {data[field]}")
            
            result = data.get("result", "")
            print(f"   Result length: {len(result)}")
            print(f"   Result type: {type(result)}")
            
            # Analyze the result content
            if isinstance(result, str):
                import re
                
                # Count different patterns
                found_match = re.search(r'Found (\d+) relevant code section', result)
                context_matches = re.findall(r'## Context \d+', result)
                code_blocks = result.count("```") // 2
                
                print(f"   'Found X relevant' match: {found_match.group(1) if found_match else 'None'}")
                print(f"   '## Context X' matches: {len(context_matches)}")
                print(f"   Code blocks: {code_blocks}")
                
                # Show first 500 characters
                print(f"\n   First 500 characters:")
                print(f"   {result[:500]}...")
                
            else:
                print(f"   Result is not a string: {result}")
        else:
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"   Exception: {e}")
    
    print("\n2. Testing alternative query...")
    payload2 = {
        "query": "tmwmem functions",
        "codebase_name": "utils", 
        "n_results": 10
    }
    
    try:
        response = requests.post(
            f"{server_url}/tools/get_optimized_context",
            json=payload2,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            
            import re
            found_match = re.search(r'Found (\d+) relevant code section', result)
            context_matches = re.findall(r'## Context \d+', result)
            
            print(f"   Query: {payload2['query']}")
            print(f"   'Found X relevant': {found_match.group(1) if found_match else 'None'}")
            print(f"   '## Context X' matches: {len(context_matches)}")
            
    except Exception as e:
        print(f"   Exception: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Server response debug completed!")

if __name__ == "__main__":
    debug_server_response()

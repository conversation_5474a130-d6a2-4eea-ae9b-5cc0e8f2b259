{"data_mtime": 1751398262, "dep_lines": [36, 37, 38, 39, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 30, 30, 30, 30], "dependencies": ["operator", "sys", "traceback", "weakref", "builtins", "_frozen_importlib", "abc", "typing", "typing_extensions"], "hash": "a7614ce2e826e1082fbf48d1ced6b62634a81809", "id": "blinker._<PERSON>ef", "ignore_all": true, "interface_hash": "1926e1ebfd92fb5b3986ebd9bac36ccc09c4366a", "mtime": 1708667620, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\blinker\\_saferef.py", "plugin_data": null, "size": 9096, "suppressed": [], "version_id": "1.15.0"}
{"data_mtime": 1751398266, "dep_lines": [14, 16, 11, 1, 3, 4, 5, 6, 7, 8, 9, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 5, 5, 10, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.sansio.response", "flask.sansio.app", "werkzeug.http", "__future__", "dataclasses", "decimal", "json", "typing", "uuid", "weakref", "datetime", "builtins", "_frozen_importlib", "abc", "flask.sansio", "flask.sansio.scaffold", "werkzeug", "werkzeug.sansio"], "hash": "2d18f8f4d71dcb3bb3fc830eeec1898a7b53ca71", "id": "flask.json.provider", "ignore_all": true, "interface_hash": "d49516d7657fab0f8ddcaf08adaaaef3781ad8d4", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\json\\provider.py", "plugin_data": null, "size": 7646, "suppressed": [], "version_id": "1.15.0"}
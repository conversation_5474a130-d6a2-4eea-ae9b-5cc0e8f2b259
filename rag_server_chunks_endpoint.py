#!/usr/bin/env python3
"""
RAG Server Enhancement: Add endpoint to get ALL chunks for codebase analysis.
This would be added to the main RAG server code.
"""

from flask import Flask, request, jsonify
import json
from typing import List, Dict

def add_chunks_endpoint(app: Flask, vector_db):
    """Add endpoint to get all chunks for a codebase"""
    
    @app.route('/codebases/<codebase_name>/all_chunks', methods=['GET'])
    def get_all_chunks(codebase_name: str):
        """Get ALL chunks for a codebase for analysis purposes"""
        try:
            # Get pagination parameters
            offset = int(request.args.get('offset', 0))
            limit = int(request.args.get('limit', 1000))  # Default 1000 chunks per request
            
            # Query the vector database for ALL chunks of this codebase
            # This would use the vector DB's native query capabilities
            chunks = vector_db.get_all_chunks_for_codebase(
                codebase_name=codebase_name,
                offset=offset,
                limit=limit
            )
            
            # Get total count for pagination
            total_chunks = vector_db.count_chunks_for_codebase(codebase_name)
            
            # Format chunks for analysis
            formatted_chunks = []
            for chunk in chunks:
                formatted_chunks.append({
                    'content': chunk.get('content', ''),
                    'metadata': {
                        'file_path': chunk.get('file_path', ''),
                        'chunk_id': chunk.get('chunk_id', ''),
                        'language': chunk.get('language', ''),
                        'semantic_tags': chunk.get('semantic_tags', []),
                        'function_names': chunk.get('function_names', []),
                        'complexity': chunk.get('complexity', 'unknown'),
                        'quality': chunk.get('quality', 'unknown')
                    }
                })
            
            return jsonify({
                'codebase': codebase_name,
                'chunks': formatted_chunks,
                'pagination': {
                    'offset': offset,
                    'limit': limit,
                    'total': total_chunks,
                    'has_more': offset + limit < total_chunks
                }
            })
            
        except Exception as e:
            return jsonify({
                'error': f'Failed to get chunks: {str(e)}'
            }), 500
    
    @app.route('/codebases/<codebase_name>/chunk_count', methods=['GET'])
    def get_chunk_count(codebase_name: str):
        """Get total number of chunks for a codebase"""
        try:
            count = vector_db.count_chunks_for_codebase(codebase_name)
            return jsonify({
                'codebase': codebase_name,
                'total_chunks': count
            })
        except Exception as e:
            return jsonify({
                'error': f'Failed to count chunks: {str(e)}'
            }), 500

# Example vector DB interface (would be implemented based on actual DB)
class VectorDBInterface:
    """Interface for vector database operations"""
    
    def get_all_chunks_for_codebase(self, codebase_name: str, offset: int = 0, limit: int = 1000) -> List[Dict]:
        """Get all chunks for a codebase with pagination"""
        # This would be implemented based on your actual vector database
        # Examples for different databases:
        
        # For ChromaDB:
        # collection = self.client.get_collection(codebase_name)
        # results = collection.get(offset=offset, limit=limit)
        
        # For Pinecone:
        # index = self.client.Index(codebase_name)
        # results = index.query(vector=[0]*dimension, top_k=limit, include_metadata=True)
        
        # For Weaviate:
        # results = self.client.query.get(codebase_name).with_limit(limit).with_offset(offset).do()
        
        # Placeholder implementation
        return []
    
    def count_chunks_for_codebase(self, codebase_name: str) -> int:
        """Count total chunks for a codebase"""
        # This would be implemented based on your actual vector database
        return 0

# Example usage in main RAG server
if __name__ == "__main__":
    app = Flask(__name__)
    
    # Initialize your vector database
    vector_db = VectorDBInterface()
    
    # Add the chunks endpoint
    add_chunks_endpoint(app, vector_db)
    
    print("🚀 RAG Server with chunks endpoint running")
    print("📋 New endpoints:")
    print("   GET /codebases/<name>/all_chunks - Get all chunks")
    print("   GET /codebases/<name>/chunk_count - Get chunk count")
    
    app.run(host='0.0.0.0', port=5002, debug=True)

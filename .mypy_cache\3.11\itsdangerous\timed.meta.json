{"data_mtime": 1751398263, "dep_lines": [7, 12, 15, 16, 1, 2, 4, 23, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 5, 25, 5, 30, 30, 30], "dependencies": ["itsdangerous.encoding", "itsdangerous.exc", "itsdangerous.serializer", "itsdangerous.signer", "time", "typing", "datetime", "typing_extensions", "builtins", "_frozen_importlib", "abc", "types"], "hash": "69fb9f397e24c1f5e4f3493f60c3a71576095c30", "id": "itsdangerous.timed", "ignore_all": true, "interface_hash": "d4f6511fe44b46ecc29ec00b6a2633bb3a9330bb", "mtime": 1708667610, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\itsdangerous\\timed.py", "plugin_data": null, "size": 8174, "suppressed": [], "version_id": "1.15.0"}
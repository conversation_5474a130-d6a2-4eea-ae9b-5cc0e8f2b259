{".class": "MypyFile", "_fullname": "test_suite", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.AsyncMock", "kind": "Gdef"}, "BaseTestCase": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["unittest.case.TestCase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_suite.BaseTestCase", "name": "BaseTestCase", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_suite.BaseTestCase", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_suite", "mro": ["test_suite.BaseTestCase", "unittest.case.TestCase", "builtins.object"], "names": {".class": "SymbolTable", "assert_json_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.BaseTestCase.assert_json_response", "name": "assert_json_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["test_suite.BaseTestCase", "requests.models.Response"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_json_response of BaseTestCase", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "assert_response_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "response", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.BaseTestCase.assert_response_success", "name": "assert_response_success", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "response", "message"], "arg_types": ["test_suite.BaseTestCase", "requests.models.Response", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "assert_response_success of BaseTestCase", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.BaseTestCase.config", "name": "config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.BaseTestCase.session", "name": "session", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setUp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.BaseTestCase.setUp", "name": "setUp", "type": null}}, "tearDown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.BaseTestCase.tearDown", "name": "tearDown", "type": null}}, "wait_for_server": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.BaseTestCase.wait_for_server", "name": "wait_for_server", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "url", "timeout"], "arg_types": ["test_suite.BaseTestCase", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wait_for_server of BaseTestCase", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_suite.BaseTestCase.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_suite.BaseTestCase", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CacheConfig": {".class": "SymbolTableNode", "cross_ref": "open_webui_code_analyzer_tool.CacheConfig", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "IntelligentCache": {".class": "SymbolTableNode", "cross_ref": "open_webui_code_analyzer_tool.IntelligentCache", "kind": "Gdef"}, "TEST_CONFIG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_suite.TEST_CONFIG", "name": "TEST_CONFIG", "type": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "TestCodeAnalyzerServer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["test_suite.BaseTestCase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_suite.TestCodeAnalyzerServer", "name": "TestCodeAnalyzerServer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_suite", "mro": ["test_suite.TestCodeAnalyzerServer", "test_suite.BaseTestCase", "unittest.case.TestCase", "builtins.object"], "names": {".class": "SymbolTable", "server_url": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestCodeAnalyzerServer.server_url", "name": "server_url", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setUp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.setUp", "name": "setUp", "type": null}}, "test_codebase_selection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.test_codebase_selection", "name": "test_codebase_selection", "type": null}}, "test_codebase_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.test_codebase_stats", "name": "test_codebase_stats", "type": null}}, "test_enhanced_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.test_enhanced_search", "name": "test_enhanced_search", "type": null}}, "test_get_optimized_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.test_get_optimized_context", "name": "test_get_optimized_context", "type": null}}, "test_health_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.test_health_endpoint", "name": "test_health_endpoint", "type": null}}, "test_list_codebases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.test_list_codebases", "name": "test_list_codebases", "type": null}}, "test_server_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestCodeAnalyzerServer.test_server_performance", "name": "test_server_performance", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_suite.TestCodeAnalyzerServer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_suite.TestCodeAnalyzerServer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestIntegration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["test_suite.BaseTestCase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_suite.TestIntegration", "name": "TestIntegration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_suite.TestIntegration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_suite", "mro": ["test_suite.TestIntegration", "test_suite.BaseTestCase", "unittest.case.TestCase", "builtins.object"], "names": {".class": "SymbolTable", "mock_emitter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestIntegration.mock_emitter", "name": "mock_emitter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "plugin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestIntegration.plugin", "name": "plugin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setUp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntegration.setUp", "name": "setUp", "type": null}}, "test_automatic_context_injection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntegration.test_automatic_context_injection", "name": "test_automatic_context_injection", "type": null}}, "test_cache_statistics_integration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntegration.test_cache_statistics_integration", "name": "test_cache_statistics_integration", "type": null}}, "test_end_to_end_with_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntegration.test_end_to_end_with_caching", "name": "test_end_to_end_with_caching", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_suite.TestIntegration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_suite.TestIntegration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestIntelligentCaching": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["test_suite.BaseTestCase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_suite.TestIntelligentCaching", "name": "TestIntelligentCaching", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_suite.TestIntelligentCaching", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_suite", "mro": ["test_suite.TestIntelligentCaching", "test_suite.BaseTestCase", "unittest.case.TestCase", "builtins.object"], "names": {".class": "SymbolTable", "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestIntelligentCaching.cache", "name": "cache", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cache_config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestIntelligentCaching.cache_config", "name": "cache_config", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setUp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntelligentCaching.setUp", "name": "setUp", "type": null}}, "tearDown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntelligentCaching.tearDown", "name": "tearDown", "type": null}}, "temp_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestIntelligentCaching.temp_dir", "name": "temp_dir", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_async_cache_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntelligentCaching.test_async_cache_methods", "name": "test_async_cache_methods", "type": null}}, "test_cache_initialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntelligentCaching.test_cache_initialization", "name": "test_cache_initialization", "type": null}}, "test_cache_invalidation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntelligentCaching.test_cache_invalidation", "name": "test_cache_invalidation", "type": null}}, "test_cache_key_generation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_suite.TestIntelligentCaching.test_cache_key_generation", "name": "test_cache_key_generation", "type": null}}, "test_cache_performance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_suite.TestIntelligentCaching.test_cache_performance", "name": "test_cache_performance", "type": null}}, "test_cache_statistics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestIntelligentCaching.test_cache_statistics", "name": "test_cache_statistics", "type": null}}, "test_cache_with_filters": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_suite.TestIntelligentCaching.test_cache_with_filters", "name": "test_cache_with_filters", "type": null}}, "test_memory_cache_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_suite.TestIntelligentCaching.test_memory_cache_operations", "name": "test_memory_cache_operations", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_suite.TestIntelligentCaching.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_suite.TestIntelligentCaching", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestOpenWebUIPlugin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["test_suite.BaseTestCase"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_suite.TestOpenWebUIPlugin", "name": "TestOpenWebUIPlugin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_suite", "mro": ["test_suite.TestOpenWebUIPlugin", "test_suite.BaseTestCase", "unittest.case.TestCase", "builtins.object"], "names": {".class": "SymbolTable", "mock_emitter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestOpenWebUIPlugin.mock_emitter", "name": "mock_emitter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "plugin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestOpenWebUIPlugin.plugin", "name": "plugin", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "setUp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.setUp", "name": "setUp", "type": null}}, "test_codebase_management_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.test_codebase_management_async", "name": "test_codebase_management_async", "type": null}}, "test_deprecated_ask_about_code_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.test_deprecated_ask_about_code_async", "name": "test_deprecated_ask_about_code_async", "type": null}}, "test_get_code_context_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.test_get_code_context_async", "name": "test_get_code_context_async", "type": null}}, "test_help_system_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.test_help_system_async", "name": "test_help_system_async", "type": null}}, "test_plugin_initialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.test_plugin_initialization", "name": "test_plugin_initialization", "type": null}}, "test_query_intent_detection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.test_query_intent_detection", "name": "test_query_intent_detection", "type": null}}, "test_smart_code_context_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestOpenWebUIPlugin.test_smart_code_context_async", "name": "test_smart_code_context_async", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_suite.TestOpenWebUIPlugin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_suite.TestOpenWebUIPlugin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestSuiteRunner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_suite.TestSuiteRunner", "name": "TestSuite<PERSON><PERSON>ner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_suite.TestSuiteRunner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_suite", "mro": ["test_suite.TestSuiteRunner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestSuiteRunner.__init__", "name": "__init__", "type": null}}, "print_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestSuiteRunner.print_summary", "name": "print_summary", "type": null}}, "results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_suite.TestSuiteRunner.results", "name": "results", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "run_test_category": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "test_class", "category_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_suite.TestSuiteRunner.run_test_category", "name": "run_test_category", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_suite.TestSuiteRunner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_suite.TestSuiteRunner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tools": {".class": "SymbolTableNode", "cross_ref": "open_webui_code_analyzer_tool.Tools", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_suite.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_suite.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_suite.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_suite.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_suite.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_suite.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "categories": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_suite.categories", "name": "categories", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [1], "arg_names": ["methodName"], "arg_types": ["builtins.str"], "bound_args": ["test_suite.TestCodeAnalyzerServer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "test_suite.BaseTestCase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [1], "arg_names": ["methodName"], "arg_types": ["builtins.str"], "bound_args": ["test_suite.TestCodeAnalyzerServer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "test_suite.BaseTestCase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "category_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_suite.category_name", "name": "category_name", "type": "builtins.str"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "runner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_suite.runner", "name": "runner", "type": "test_suite.TestSuiteRunner"}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "test_arg": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_suite.test_arg", "name": "test_arg", "type": "builtins.str"}}, "test_class": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "test_suite.test_class", "name": "test_class", "type": {".class": "CallableType", "arg_kinds": [1], "arg_names": ["methodName"], "arg_types": ["builtins.str"], "bound_args": ["test_suite.TestCodeAnalyzerServer"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "test_suite.BaseTestCase", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "unittest": {".class": "SymbolTableNode", "cross_ref": "unittest", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_suite.py"}
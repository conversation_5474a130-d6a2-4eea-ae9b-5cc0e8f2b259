{".class": "MypyFile", "_fullname": "itsdangerous", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadData": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadData", "kind": "Gdef"}, "BadHeader": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadHeader", "kind": "Gdef"}, "BadPayload": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadPayload", "kind": "Gdef"}, "BadSignature": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadSignature", "kind": "Gdef"}, "BadTimeSignature": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadTimeSignature", "kind": "Gdef"}, "HMACAlgorithm": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.signer.HMACAlgorithm", "kind": "Gdef"}, "NoneAlgorithm": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.signer.NoneAlgorithm", "kind": "Gdef"}, "Serializer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.serializer.Serializer", "kind": "Gdef"}, "SignatureExpired": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.SignatureExpired", "kind": "Gdef"}, "Signer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.signer.Signer", "kind": "Gdef"}, "TimedSerializer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.timed.TimedSerializer", "kind": "Gdef"}, "TimestampSigner": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.timed.TimestampSigner", "kind": "Gdef"}, "URLSafeSerializer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.url_safe.URLSafeSerializer", "kind": "Gdef"}, "URLSafeTimedSerializer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.url_safe.URLSafeTimedSerializer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.__package__", "name": "__package__", "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.__path__", "name": "__path__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "itsdangerous.__version__", "name": "__version__", "type": "builtins.str"}}, "base64_decode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_decode", "kind": "Gdef"}, "base64_encode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_encode", "kind": "Gdef"}, "want_bytes": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.want_bytes", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\itsdangerous\\__init__.py"}
#!/usr/bin/env python3
"""
Debug script to test intent detection for memory management queries.
"""

import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

def test_intent_detection():
    """Test intent detection for various queries"""
    print("🧪 Testing Intent Detection")
    print("=" * 50)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    test_queries = [
        "how is memory managed",
        "memory management practices",
        "show me memory allocation",
        "explain memory handling",
        "get cache stats",
        "show cache statistics", 
        "get stats for utils",
        "show statistics",
        "memory management in C",
        "how does memory allocation work"
    ]
    
    for query in test_queries:
        intent = tool._detect_query_intent(query)
        is_code_related = tool._detect_code_related_query(query)
        print(f"Query: '{query}'")
        print(f"  Intent: {intent}")
        print(f"  Code-related: {is_code_related}")
        print()

if __name__ == "__main__":
    test_intent_detection()

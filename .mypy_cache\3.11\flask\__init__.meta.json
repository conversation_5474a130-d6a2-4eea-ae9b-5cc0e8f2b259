{"data_mtime": 1751398266, "dep_lines": [5, 6, 7, 8, 9, 13, 17, 28, 38, 42, 1, 3, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30], "dependencies": ["flask.json", "flask.app", "flask.blueprints", "flask.config", "flask.ctx", "flask.globals", "flask.helpers", "flask.signals", "flask.templating", "flask.wrappers", "__future__", "typing", "builtins", "_frozen_importlib", "abc"], "hash": "796763af7b3ff551b69da965fe9768008295ac55", "id": "flask", "ignore_all": true, "interface_hash": "4a17c649649f239b76db3f5e0a20b52ee8599153", "mtime": 1708667823, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\__init__.py", "plugin_data": null, "size": 2625, "suppressed": [], "version_id": "1.15.0"}
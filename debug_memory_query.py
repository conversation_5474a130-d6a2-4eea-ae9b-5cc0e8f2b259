#!/usr/bin/env python3
"""
Debug script to test the exact memory management query routing.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_memory_query():
    """Test the exact memory management query"""
    print("🧪 Testing Memory Management Query Routing")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    select_result = await tool.select_codebase("utils")
    print(f"   Selection result: {select_result[:100]}...")
    
    print(f"\n2. Current codebase: {tool.valves.current_codebase}")
    
    # Test the main entry point with memory query
    print("\n3. Testing main entry point with 'how is memory managed'...")
    try:
        result = await tool.__call__("how is memory managed")
        print(f"   Result length: {len(result)} characters")
        print(f"   First 300 characters: {result[:300]}...")
        
        if "cache" in result.lower():
            print("   ⚠️ WARNING: Result contains cache-related content!")
        if "memory management" in result.lower():
            print("   ✅ Result contains memory management content")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test smart_code_context directly
    print("\n4. Testing smart_code_context directly...")
    try:
        result = await tool.smart_code_context("how is memory managed")
        print(f"   Result length: {len(result)} characters")
        print(f"   First 300 characters: {result[:300]}...")
        
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 Memory query test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_memory_query())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)

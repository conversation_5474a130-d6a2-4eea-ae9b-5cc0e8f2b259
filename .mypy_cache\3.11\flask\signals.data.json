{".class": "MypyFile", "_fullname": "flask.signals", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Namespace": {".class": "SymbolTableNode", "cross_ref": "blinker.base.Namespace", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.signals.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.signals.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.signals.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.signals.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.signals.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.signals.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_signals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals._signals", "name": "_signals", "type": "blinker.base.Namespace"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "appcontext_popped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.appcontext_popped", "name": "appcontext_popped", "type": "blinker.base.NamedSignal"}}, "appcontext_pushed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.appcontext_pushed", "name": "appcontext_pushed", "type": "blinker.base.NamedSignal"}}, "appcontext_tearing_down": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.appcontext_tearing_down", "name": "appcontext_tearing_down", "type": "blinker.base.NamedSignal"}}, "before_render_template": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.before_render_template", "name": "before_render_template", "type": "blinker.base.NamedSignal"}}, "got_request_exception": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.got_request_exception", "name": "got_request_exception", "type": "blinker.base.NamedSignal"}}, "message_flashed": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.message_flashed", "name": "message_flashed", "type": "blinker.base.NamedSignal"}}, "request_finished": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.request_finished", "name": "request_finished", "type": "blinker.base.NamedSignal"}}, "request_started": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.request_started", "name": "request_started", "type": "blinker.base.NamedSignal"}}, "request_tearing_down": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.request_tearing_down", "name": "request_tearing_down", "type": "blinker.base.NamedSignal"}}, "template_rendered": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "flask.signals.template_rendered", "name": "template_rendered", "type": "blinker.base.NamedSignal"}}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\signals.py"}
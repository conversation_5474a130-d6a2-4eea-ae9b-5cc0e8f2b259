{".class": "MypyFile", "_fullname": "itsdangerous.timed", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadSignature": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadSignature", "kind": "Gdef"}, "BadTimeSignature": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadTimeSignature", "kind": "Gdef"}, "Serializer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.serializer.Serializer", "kind": "Gdef"}, "SignatureExpired": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.SignatureExpired", "kind": "Gdef"}, "Signer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.signer.Signer", "kind": "Gdef"}, "TimedSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["itsdangerous.serializer.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.timed.TimedSerializer", "name": "TimedSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimedSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.timed", "mro": ["itsdangerous.timed.TimedSerializer", "itsdangerous.serializer.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "default_signer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "itsdangerous.timed.TimedSerializer.default_signer", "name": "default_signer", "type": {".class": "TypeType", "item": "itsdangerous.timed.TimestampSigner"}}}, "iter_unsigners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimedSerializer.iter_unsigners", "name": "iter_unsigners", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "salt"], "arg_types": ["itsdangerous.timed.TimedSerializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_unsigners of TimedSerializer", "ret_type": {".class": "Instance", "args": ["itsdangerous.timed.TimestampSigner"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "s", "max_age", "return_timestamp", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimedSerializer.loads", "name": "loads", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "s", "max_age", "return_timestamp", "salt"], "arg_types": ["itsdangerous.timed.TimedSerializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, "builtins.bool", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loads of TimedSerializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loads_unsafe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "s", "max_age", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimedSerializer.loads_unsafe", "name": "loads_unsafe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "s", "max_age", "salt"], "arg_types": ["itsdangerous.timed.TimedSerializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loads_unsafe of TimedSerializer", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.timed.TimedSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.timed.TimedSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TimestampSigner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["itsdangerous.signer.Signer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.timed.TimestampSigner", "name": "TimestampSigner", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimestampSigner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.timed", "mro": ["itsdangerous.timed.TimestampSigner", "itsdangerous.signer.Signer", "builtins.object"], "names": {".class": "SymbolTable", "get_timestamp": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimestampSigner.get_timestamp", "name": "get_timestamp", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["itsdangerous.timed.TimestampSigner"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_timestamp of TimestampSigner", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimestampSigner.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of Timestamp<PERSON>ign<PERSON>", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "timestamp_to_datetime": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "ts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimestampSigner.timestamp_to_datetime", "name": "timestamp_to_datetime", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "ts"], "arg_types": ["itsdangerous.timed.TimestampSigner", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "timestamp_to_datetime of TimestampSigner", "ret_type": "datetime.datetime", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unsign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimestampSigner.unsign", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "itsdangerous.timed.TimestampSigner.unsign", "name": "unsign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of TimestampSigner", "ret_type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "datetime.datetime"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "builtins.bytes"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "itsdangerous.timed.TimestampSigner.unsign", "name": "unsign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of TimestampSigner", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "itsdangerous.timed.TimestampSigner.unsign", "name": "unsign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of TimestampSigner", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "itsdangerous.timed.TimestampSigner.unsign", "name": "unsign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of TimestampSigner", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "datetime.datetime"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "itsdangerous.timed.TimestampSigner.unsign", "name": "unsign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of TimestampSigner", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "datetime.datetime"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of TimestampSigner", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "signed_value", "max_age", "return_timestamp"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of TimestampSigner", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "datetime.datetime"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "signed_value", "max_age"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.timed.TimestampSigner.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "signed_value", "max_age"], "arg_types": ["itsdangerous.timed.TimestampSigner", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_opt_int"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate of TimestampSigner", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.timed.TimestampSigner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.timed.TimestampSigner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.timed.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.timed.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.timed.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.timed.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.timed.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.timed.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "_t_opt_int": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.timed._t_opt_int", "line": 20, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_t_opt_str_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.timed._t_opt_str_bytes", "line": 19, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.timed._t_str_bytes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_t_str_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.timed._t_str_bytes", "line": 18, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}}}, "_te": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "base64_decode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_decode", "kind": "Gdef"}, "base64_encode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_encode", "kind": "Gdef"}, "bytes_to_int": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.bytes_to_int", "kind": "Gdef"}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "int_to_bytes": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.int_to_bytes", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "timezone": {".class": "SymbolTableNode", "cross_ref": "datetime.timezone", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "want_bytes": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.want_bytes", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\itsdangerous\\timed.py"}
{".class": "MypyFile", "_fullname": "rag_server_chunks_endpoint", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "Flask": {".class": "SymbolTableNode", "cross_ref": "flask.app.Flask", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "VectorDBInterface": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "rag_server_chunks_endpoint.VectorDBInterface", "name": "VectorDBInterface", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "rag_server_chunks_endpoint.VectorDBInterface", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "rag_server_chunks_endpoint", "mro": ["rag_server_chunks_endpoint.VectorDBInterface", "builtins.object"], "names": {".class": "SymbolTable", "count_chunks_for_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "codebase_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rag_server_chunks_endpoint.VectorDBInterface.count_chunks_for_codebase", "name": "count_chunks_for_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "codebase_name"], "arg_types": ["rag_server_chunks_endpoint.VectorDBInterface", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "count_chunks_for_codebase of VectorDBInterface", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_all_chunks_for_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "codebase_name", "offset", "limit"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rag_server_chunks_endpoint.VectorDBInterface.get_all_chunks_for_codebase", "name": "get_all_chunks_for_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "codebase_name", "offset", "limit"], "arg_types": ["rag_server_chunks_endpoint.VectorDBInterface", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_all_chunks_for_codebase of VectorDBInterface", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "rag_server_chunks_endpoint.VectorDBInterface.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "rag_server_chunks_endpoint.VectorDBInterface", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rag_server_chunks_endpoint.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rag_server_chunks_endpoint.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rag_server_chunks_endpoint.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rag_server_chunks_endpoint.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rag_server_chunks_endpoint.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "rag_server_chunks_endpoint.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "add_chunks_endpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["app", "vector_db"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "rag_server_chunks_endpoint.add_chunks_endpoint", "name": "add_chunks_endpoint", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["app", "vector_db"], "arg_types": ["flask.app.Flask", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "add_chunks_endpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "app": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rag_server_chunks_endpoint.app", "name": "app", "type": "flask.app.Flask"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "jsonify": {".class": "SymbolTableNode", "cross_ref": "flask.json.jsonify", "kind": "Gdef"}, "request": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request", "kind": "Gdef"}, "vector_db": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "rag_server_chunks_endpoint.vector_db", "name": "vector_db", "type": "rag_server_chunks_endpoint.VectorDBInterface"}}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\rag_server_chunks_endpoint.py"}
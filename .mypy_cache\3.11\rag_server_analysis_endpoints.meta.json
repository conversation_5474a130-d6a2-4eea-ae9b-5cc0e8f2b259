{"data_mtime": 1751399883, "dep_lines": [7, 8, 9, 10, 11, 12, 13, 14, 125, 126, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 10, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask", "json", "os", "asyncio", "typing", "logging", "datetime", "vector_db_interface", "collections", "re", "builtins", "_frozen_importlib", "abc", "flask.app", "flask.blueprints", "flask.sansio", "flask.sansio.app", "flask.sansio.blueprints", "flask.sansio.scaffold"], "hash": "012fa46c4a60819641a6825befe56fee3baf790b", "id": "rag_server_analysis_endpoints", "ignore_all": true, "interface_hash": "4945438f9112dfe13caf7c2c01968c37801f9f5c", "mtime": 1751399810, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\rag_server_analysis_endpoints.py", "plugin_data": null, "size": 22147, "suppressed": [], "version_id": "1.15.0"}
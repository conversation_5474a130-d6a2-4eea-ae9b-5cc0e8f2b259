{".class": "MypyFile", "_fullname": "jinja2.parser", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.parser.Parser", "name": "<PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.parser", "mro": ["jinja2.parser.Parser", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "environment", "source", "name", "filename", "state"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1], "arg_names": ["self", "environment", "source", "name", "filename", "state"], "arg_types": ["jinja2.parser.Parser", "jinja2.environment.Environment", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Parse<PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_end_token_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.parser.Parser._end_token_stack", "name": "_end_token_stack", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_fail_ut_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "end_token_stack", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser._fail_ut_eof", "name": "_fail_ut_eof", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "name", "end_token_stack", "lineno"], "arg_types": ["jinja2.parser.Parser", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fail_ut_eof of Parser", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_last_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.parser.Parser._last_identifier", "name": "_last_identifier", "type": "builtins.int"}}, "_tag_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.parser.Parser._tag_stack", "name": "_tag_stack", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "closed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.parser.Parser.closed", "name": "closed", "type": "builtins.bool"}}, "environment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.parser.Parser.environment", "name": "environment", "type": "jinja2.environment.Environment"}}, "extensions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.parser.Parser.extensions", "name": "extensions", "type": {".class": "Instance", "args": ["builtins.str", {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.Node", {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "fail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "msg", "lineno", "exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.fail", "name": "fail", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "msg", "lineno", "exc"], "arg_types": ["jinja2.parser.Parser", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeType", "item": "jinja2.exceptions.TemplateSyntaxError"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fail of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fail_eof": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "end_tokens", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.fail_eof", "name": "fail_eof", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "end_tokens", "lineno"], "arg_types": ["jinja2.parser.Parser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fail_eof of Parser", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fail_unknown_tag": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.fail_unknown_tag", "name": "fail_unknown_tag", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "name", "lineno"], "arg_types": ["jinja2.parser.Parser", "builtins.str", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fail_unknown_tag of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.parser.Parser.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "free_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.free_identifier", "name": "free_identifier", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "lineno"], "arg_types": ["jinja2.parser.Parser", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "free_identifier of Parser", "ret_type": "jinja2.nodes.InternalName", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_tuple_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "extra_end_rules"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.is_tuple_end", "name": "is_tuple_end", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "extra_end_rules"], "arg_types": ["jinja2.parser.Parser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_tuple_end of Parser", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.parser.Parser.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "parse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse", "name": "parse", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse of Parser", "ret_type": "jinja2.nodes.Template", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_and": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_and", "name": "parse_and", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_and of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_assign_target": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_assign_target", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "with_tuple", "name_only", "extra_end_rules", "with_namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "jinja2.parser.Parser.parse_assign_target", "name": "parse_assign_target", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "with_tuple", "name_only", "extra_end_rules", "with_namespace"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_assign_target of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.NSRef", "jinja2.nodes.Name", "jinja2.nodes.Tuple"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "with_tuple", "name_only"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "jinja2.parser.Parser.parse_assign_target", "name": "parse_assign_target", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "with_tuple", "name_only"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_assign_target of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Name", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "jinja2.parser.Parser.parse_assign_target", "name": "parse_assign_target", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "with_tuple", "name_only"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_assign_target of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Name", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "with_tuple", "name_only", "extra_end_rules", "with_namespace"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "jinja2.parser.Parser.parse_assign_target", "name": "parse_assign_target", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "with_tuple", "name_only", "extra_end_rules", "with_namespace"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_assign_target of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.NSRef", "jinja2.nodes.Name", "jinja2.nodes.Tuple"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "jinja2.parser.Parser.parse_assign_target", "name": "parse_assign_target", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "with_tuple", "name_only", "extra_end_rules", "with_namespace"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_assign_target of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.NSRef", "jinja2.nodes.Name", "jinja2.nodes.Tuple"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "with_tuple", "name_only"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", {".class": "LiteralType", "fallback": "builtins.bool", "value": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_assign_target of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Name", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "with_tuple", "name_only", "extra_end_rules", "with_namespace"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_assign_target of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.NSRef", "jinja2.nodes.Name", "jinja2.nodes.Tuple"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "parse_autoescape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_autoescape", "name": "parse_autoescape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_autoescape of Parser", "ret_type": "jinja2.nodes.Scope", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_block", "name": "parse_block", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_block of Parser", "ret_type": "jinja2.nodes.Block", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_call", "name": "parse_call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.parser.Parser", "jinja2.nodes.Expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_call of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Call", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_call_args": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_call_args", "name": "parse_call_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_call_args of Parser", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.tuple"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_call_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_call_block", "name": "parse_call_block", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_call_block of Parser", "ret_type": "jinja2.nodes.CallBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_compare", "name": "parse_compare", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_compare of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_concat", "name": "parse_concat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_concat of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_condexpr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_condexpr", "name": "parse_condexpr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_condexpr of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_dict", "name": "parse_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_dict of Parser", "ret_type": "jinja2.nodes.Dict", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_expression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "with_condexpr"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_expression", "name": "parse_expression", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "with_condexpr"], "arg_types": ["jinja2.parser.Parser", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_expression of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_extends": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_extends", "name": "parse_extends", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_extends of Parser", "ret_type": "jinja2.nodes.Extends", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "start_inline"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_filter", "name": "parse_filter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "start_inline"], "arg_types": ["jinja2.parser.Parser", {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_filter of Parser", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.Expr", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_filter_block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_filter_block", "name": "parse_filter_block", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_filter_block of Parser", "ret_type": "jinja2.nodes.FilterBlock", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_filter_expr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_filter_expr", "name": "parse_filter_expr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.parser.Parser", "jinja2.nodes.Expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_filter_expr of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_for": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_for", "name": "parse_for", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_for of Parser", "ret_type": "jinja2.nodes.For", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_from": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_from", "name": "parse_from", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_from of Parser", "ret_type": "jinja2.nodes.FromImport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_if": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_if", "name": "parse_if", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_if of Parser", "ret_type": "jinja2.nodes.If", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_import": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_import", "name": "parse_import", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_import of Parser", "ret_type": "jinja2.nodes.Import", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_import_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "default"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_import_context", "name": "parse_import_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "default"], "arg_types": ["jinja2.parser.Parser", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser._ImportInclude", "id": -1, "name": "_ImportInclude", "namespace": "jinja2.parser.Parser.parse_import_context", "upper_bound": "builtins.object", "values": ["jinja2.nodes.Import", "jinja2.nodes.Include"], "variance": 0}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_import_context of Parser", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser._ImportInclude", "id": -1, "name": "_ImportInclude", "namespace": "jinja2.parser.Parser.parse_import_context", "upper_bound": "builtins.object", "values": ["jinja2.nodes.Import", "jinja2.nodes.Include"], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser._ImportInclude", "id": -1, "name": "_ImportInclude", "namespace": "jinja2.parser.Parser.parse_import_context", "upper_bound": "builtins.object", "values": ["jinja2.nodes.Import", "jinja2.nodes.Include"], "variance": 0}]}}}, "parse_include": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_include", "name": "parse_include", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_include of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Include", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_list", "name": "parse_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_list of Parser", "ret_type": "jinja2.nodes.List", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_macro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_macro", "name": "parse_macro", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_macro of Parser", "ret_type": "jinja2.nodes.Macro", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_math1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_math1", "name": "parse_math1", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_math1 of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_math2": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_math2", "name": "parse_math2", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_math2 of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_not": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_not", "name": "parse_not", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_not of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_or": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_or", "name": "parse_or", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_or of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_postfix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_postfix", "name": "parse_postfix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.parser.Parser", "jinja2.nodes.Expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_postfix of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_pow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_pow", "name": "parse_pow", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_pow of Parser", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_primary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_primary", "name": "parse_primary", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_primary of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_print": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_print", "name": "parse_print", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_print of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Output", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_set": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_set", "name": "parse_set", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_set of Parser", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.Assign", "jinja2.nodes.AssignBlock"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_signature", "name": "parse_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.parser.Parser", {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser._MacroCall", "id": -1, "name": "_MacroCall", "namespace": "jinja2.parser.Parser.parse_signature", "upper_bound": "builtins.object", "values": ["jinja2.nodes.Macro", "jinja2.nodes.CallBlock"], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_signature of <PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser._MacroCall", "id": -1, "name": "_MacroCall", "namespace": "jinja2.parser.Parser.parse_signature", "upper_bound": "builtins.object", "values": ["jinja2.nodes.Macro", "jinja2.nodes.CallBlock"], "variance": 0}]}}}, "parse_statement": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_statement", "name": "parse_statement", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_statement of <PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.Node", {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_statements": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "end_tokens", "drop_needle"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_statements", "name": "parse_statements", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "end_tokens", "drop_needle"], "arg_types": ["jinja2.parser.Parser", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_statements of <PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_subscribed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_subscribed", "name": "parse_subscribed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_subscribed of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_subscript": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_subscript", "name": "parse_subscript", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.parser.Parser", "jinja2.nodes.Expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_subscript of Parser", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.Getattr", "jinja2.nodes.Getitem"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_test", "name": "parse_test", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.parser.Parser", "jinja2.nodes.Expr"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_test of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "simplified", "with_condexpr", "extra_end_rules", "explicit_parentheses"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_tuple", "name": "parse_tuple", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "simplified", "with_condexpr", "extra_end_rules", "explicit_parentheses"], "arg_types": ["jinja2.parser.Parser", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_tuple of Parser", "ret_type": {".class": "UnionType", "items": ["jinja2.nodes.Tuple", "jinja2.nodes.Expr"], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_unary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "with_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_unary", "name": "parse_unary", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "with_filter"], "arg_types": ["jinja2.parser.Parser", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_unary of <PERSON><PERSON><PERSON>", "ret_type": "jinja2.nodes.Expr", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_with": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.parse_with", "name": "parse_with", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.parser.Parser"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parse_with of Parse<PERSON>", "ret_type": "jinja2.nodes.With", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.parser.Parser.stream", "name": "stream", "type": "jinja2.lexer.TokenStream"}}, "subparse": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "end_tokens"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.parser.Parser.subparse", "name": "subparse", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "end_tokens"], "arg_types": ["jinja2.parser.Parser", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "subparse of Parser", "ret_type": {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser.Parser.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.parser.Parser", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TemplateAssertionError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateAssertionError", "kind": "Gdef"}, "TemplateSyntaxError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateSyntaxError", "kind": "Gdef"}, "_ImportInclude": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser._ImportInclude", "name": "_ImportInclude", "upper_bound": "builtins.object", "values": ["jinja2.nodes.Import", "jinja2.nodes.Include"], "variance": 0}}, "_MacroCall": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.parser._MacroCall", "name": "_MacroCall", "upper_bound": "builtins.object", "values": ["jinja2.nodes.Macro", "jinja2.nodes.CallBlock"], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.parser.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.parser.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.parser.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.parser.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.parser.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.parser.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_compare_operators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "jinja2.parser._compare_operators", "name": "_compare_operators", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "_math_nodes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "jinja2.parser._math_nodes", "name": "_math_nodes", "type": {".class": "Instance", "args": ["builtins.str", {".class": "TypeType", "item": "jinja2.nodes.Expr"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_statement_keywords": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "jinja2.parser._statement_keywords", "name": "_statement_keywords", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.frozenset"}}}, "describe_token": {".class": "SymbolTableNode", "cross_ref": "jinja2.lexer.describe_token", "kind": "Gdef"}, "describe_token_expr": {".class": "SymbolTableNode", "cross_ref": "jinja2.lexer.describe_token_expr", "kind": "Gdef"}, "nodes": {".class": "SymbolTableNode", "cross_ref": "jinja2.nodes", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "te": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\jinja2\\parser.py"}
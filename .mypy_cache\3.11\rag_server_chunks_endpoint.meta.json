{"data_mtime": 1751399462, "dep_lines": [7, 8, 9, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask", "json", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "flask.app", "flask.globals", "flask.json", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "flask.wrappers", "os", "typing_extensions", "werkzeug", "werkzeug.datastructures", "werkzeug.datastructures.headers", "werkzeug.datastructures.structures", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.request", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "cb775bc5c1f10d76d642cf28634eeac65862940e", "id": "rag_server_chunks_endpoint", "ignore_all": false, "interface_hash": "9ccd409a6642716cb80aae6a44275e92e790f703", "mtime": 1751399460, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\rag_server_chunks_endpoint.py", "plugin_data": null, "size": 4753, "suppressed": [], "version_id": "1.15.0"}
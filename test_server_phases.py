#!/usr/bin/env python3
"""
Comprehensive test of all 3 server phases for dynamic codebase analysis.
"""

import asyncio
import requests
import json
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

async def test_all_server_phases():
    """Test all 3 phases of server implementation"""
    print("🚀 TESTING ALL 3 SERVER PHASES")
    print("=" * 60)
    
    base_url = "http://home-ai-server.local:5002"
    
    # Phase 1: Test Vector Database Interface
    print("\n📋 PHASE 1: VECTOR DATABASE INTERFACE")
    print("-" * 40)
    
    # Test chunk retrieval endpoint
    try:
        response = requests.get(f"{base_url}/api/v1/codebases/utils/all_chunks?limit=10")
        
        if response.status_code == 200:
            data = response.json()
            chunks = data.get('chunks', [])
            pagination = data.get('pagination', {})
            
            print(f"✅ Chunk retrieval working: {len(chunks)} chunks")
            print(f"   Total chunks available: {pagination.get('total', 'unknown')}")
            print(f"   Has more: {pagination.get('has_more', False)}")
            
            if chunks:
                sample_chunk = chunks[0]
                content_length = len(sample_chunk.get('content', ''))
                metadata_keys = list(sample_chunk.get('metadata', {}).keys())
                print(f"   Sample chunk: {content_length} chars, metadata: {metadata_keys}")
        
        elif response.status_code == 404:
            print("⚠️ Chunk endpoint not available (expected if not implemented)")
        else:
            print(f"❌ Chunk retrieval failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Phase 1 test error: {e}")
    
    # Phase 2: Test Analysis Endpoints
    print("\n📋 PHASE 2: ANALYSIS ENDPOINTS")
    print("-" * 40)
    
    # Test analysis health
    try:
        response = requests.get(f"{base_url}/analysis/health")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Analysis service health: {data.get('status')}")
            print(f"   Features: {data.get('features', [])}")
        else:
            print(f"⚠️ Analysis health endpoint not available: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️ Analysis health test error: {e}")
    
    # Test codebase analysis
    try:
        print("\n🧠 Testing codebase analysis...")
        response = requests.post(
            f"{base_url}/api/v1/codebases/utils/analyze",
            json={"force_refresh": False}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Codebase analysis successful")
            print(f"   Source: {data.get('source', 'unknown')}")
            print(f"   Functions discovered: {data.get('functions_discovered', 0)}")
            print(f"   Domains identified: {data.get('domains_identified', [])}")
            print(f"   Enhancement rules: {data.get('enhancement_rules', 0)}")
        else:
            print(f"⚠️ Codebase analysis not available: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️ Analysis test error: {e}")
    
    # Test pattern retrieval
    try:
        response = requests.get(f"{base_url}/api/v1/codebases/utils/patterns")
        
        if response.status_code == 200:
            data = response.json()
            patterns = data.get('patterns', {})
            print(f"✅ Pattern retrieval working")
            print(f"   Functions: {len(patterns.get('functions', []))}")
            print(f"   Domains: {len(patterns.get('domains', {}))}")
            print(f"   Keywords: {len(patterns.get('keywords', []))}")
        else:
            print(f"⚠️ Pattern retrieval not available: {response.status_code}")
            
    except Exception as e:
        print(f"⚠️ Pattern test error: {e}")
    
    # Phase 3: Test Query Enhancement
    print("\n📋 PHASE 3: QUERY ENHANCEMENT")
    print("-" * 40)
    
    test_queries = [
        "memory management",
        "error handling",
        "network operations", 
        "timer functions",
        "configuration settings"
    ]
    
    enhancement_working = 0
    
    for query in test_queries:
        try:
            response = requests.post(
                f"{base_url}/api/v1/enhance_query",
                json={"query": query, "codebase_name": "utils"}
            )
            
            if response.status_code == 200:
                data = response.json()
                enhancements = data.get('enhancements', [])
                enhanced_query = data.get('enhanced_query', '')
                
                if enhancements:
                    print(f"✅ '{query}' → {enhancements[:3]}...")
                    enhancement_working += 1
                else:
                    print(f"⚠️ '{query}' → no enhancements")
            else:
                print(f"❌ Enhancement failed for '{query}': {response.status_code}")
                
        except Exception as e:
            print(f"❌ Enhancement error for '{query}': {e}")
    
    # Test integration with OpenWebUI tool
    print("\n📋 INTEGRATION TEST: OPENWEBUI TOOL")
    print("-" * 40)
    
    try:
        # Import and test the tool
        from open_webui_code_analyzer_tool import Tools
        
        tool = Tools()
        tool.valves.code_analyzer_server_url = base_url
        
        # Test that the tool can use the new endpoints
        print("🧪 Testing tool integration...")
        
        # Test chunk fetching
        chunks = await tool._fetch_codebase_chunks("utils")
        print(f"✅ Tool chunk fetching: {len(chunks)} chunks")
        
        # Test analysis trigger
        await tool._ensure_codebase_analyzed("utils")
        print(f"✅ Tool analysis trigger working")
        
        # Test dynamic enhancement
        result = await tool.get_code_context("memory management", codebase_name="utils", n_results=3)
        
        if "Dynamic enhancement applied" in str(result):
            print(f"✅ Dynamic enhancement working in tool")
        elif len(result) > 300:
            print(f"✅ Tool query working (may use static enhancement)")
        else:
            print(f"⚠️ Tool query returned limited results")
            
    except Exception as e:
        print(f"❌ Tool integration test error: {e}")
    
    # Summary and recommendations
    print("\n" + "=" * 60)
    print("📊 SERVER PHASES TEST SUMMARY")
    print("=" * 60)
    
    print(f"🎯 PHASE IMPLEMENTATION STATUS:")
    print(f"   Phase 1 (Vector DB Interface): {'✅ Ready' if 'chunks' in locals() else '⚠️ Needs Implementation'}")
    print(f"   Phase 2 (Analysis Endpoints): {'✅ Ready' if 'data' in locals() else '⚠️ Needs Implementation'}")
    print(f"   Phase 3 (Query Enhancement): {'✅ Ready' if enhancement_working > 0 else '⚠️ Needs Implementation'}")
    
    print(f"\n🔧 IMPLEMENTATION RECOMMENDATIONS:")
    
    if 'chunks' not in locals():
        print(f"   1. Add vector database interface (vector_db_interface.py)")
        print(f"   2. Add chunk retrieval endpoint (/api/v1/codebases/<name>/all_chunks)")
    
    if 'data' not in locals():
        print(f"   3. Add analysis service (rag_server_analysis_endpoints.py)")
        print(f"   4. Add analysis endpoints (/api/v1/codebases/<name>/analyze)")
    
    if enhancement_working == 0:
        print(f"   5. Add query enhancement endpoint (/api/v1/enhance_query)")
        print(f"   6. Integrate pattern caching and retrieval")
    
    if enhancement_working > 0:
        print(f"   🎉 All phases working! Enhancement success rate: {enhancement_working}/{len(test_queries)} ({enhancement_working/len(test_queries)*100:.1f}%)")
    
    print(f"\n📖 NEXT STEPS:")
    print(f"   1. Run: python server_integration_guide.py guide")
    print(f"   2. Follow migration steps for your server")
    print(f"   3. Test with: python server_integration_guide.py test")
    print(f"   4. Deploy and enjoy dynamic enhancement!")
    
    print(f"\n🚀 Server phases testing complete!")

if __name__ == "__main__":
    try:
        asyncio.run(test_all_server_phases())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

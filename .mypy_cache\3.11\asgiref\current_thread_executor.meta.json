{"data_mtime": 1751398264, "dep_lines": [4, 1, 2, 3, 5, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["concurrent.futures", "queue", "sys", "threading", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "concurrent", "concurrent.futures._base", "types"], "hash": "bb736097317df53b55f7e1123270ac43a2188217", "id": "asgiref.current_thread_executor", "ignore_all": true, "interface_hash": "3173b6469d2eb6fcc425937c905700ca3fa9acec", "mtime": 1750258710, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\asgiref\\current_thread_executor.py", "plugin_data": null, "size": 3977, "suppressed": [], "version_id": "1.15.0"}
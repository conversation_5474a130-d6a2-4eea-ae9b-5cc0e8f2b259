{"data_mtime": 1751398265, "dep_lines": [4, 1, 1, 1, 1, 1, 1], "dep_prios": [20, 10, 5, 30, 30, 30, 30], "dependencies": ["jinja2.runtime", "typing", "builtins", "_frozen_importlib", "abc", "types", "typing_extensions"], "hash": "dcc4a315066a26cc061ab89ff8b359ce58583dab", "id": "jinja2.exceptions", "ignore_all": true, "interface_hash": "ae4ca95218110abc21a67a67f5ecc7867f269f5a", "mtime": 1691929948, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\jinja2\\exceptions.py", "plugin_data": null, "size": 5071, "suppressed": [], "version_id": "1.15.0"}
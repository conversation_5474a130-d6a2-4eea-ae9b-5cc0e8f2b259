{"data_mtime": **********, "dep_lines": [7, 6, 10, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask.json.provider", "flask.globals", "flask.wrappers", "__future__", "json", "typing", "builtins", "_frozen_importlib", "abc", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.response"], "hash": "b015d9e6910062b5ea888a87f50a4b1c77fccf7f", "id": "flask.json", "ignore_all": true, "interface_hash": "02f86be97d9e0d1a20438aa8b788d9f74dfe5916", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\json\\__init__.py", "plugin_data": null, "size": 5602, "suppressed": [], "version_id": "1.15.0"}
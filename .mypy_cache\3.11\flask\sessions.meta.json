{"data_mtime": 1751398266, "dep_lines": [13, 5, 11, 18, 19, 1, 3, 4, 6, 9, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 25, 25, 5, 10, 10, 5, 5, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask.json.tag", "collections.abc", "werkzeug.datastructures", "flask.app", "flask.wrappers", "__future__", "<PERSON><PERSON><PERSON>", "typing", "datetime", "itsdangerous", "typing_extensions", "builtins", "_frozen_importlib", "_hashlib", "abc", "flask.json", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "itsdangerous.serializer", "itsdangerous.timed", "itsdangerous.url_safe", "werkzeug", "werkzeug.datastructures.mixins", "werkzeug.datastructures.structures", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers", "werkzeug.wrappers.request", "werkzeug.wrappers.response"], "hash": "06bcda8565a275ff4b1fe2da977127d9d21aa9f5", "id": "flask.sessions", "ignore_all": true, "interface_hash": "b2c9e55c96e55dc3c2e82d5791095db9f7e5e586", "mtime": 1708667823, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\sessions.py", "plugin_data": null, "size": 14518, "suppressed": [], "version_id": "1.15.0"}
{".class": "MypyFile", "_fullname": "itsdangerous.url_safe", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadPayload": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadPayload", "kind": "Gdef"}, "Serializer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.serializer.Serializer", "kind": "Gdef"}, "TimedSerializer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.timed.TimedSerializer", "kind": "Gdef"}, "URLSafeSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["itsdangerous.url_safe.URLSafeSerializerMixin", "itsdangerous.serializer.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.url_safe.URLSafeSerializer", "name": "URLSafeSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.url_safe.URLSafeSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.url_safe", "mro": ["itsdangerous.url_safe.URLSafeSerializer", "itsdangerous.url_safe.URLSafeSerializerMixin", "itsdangerous.serializer.Serializer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.url_safe.URLSafeSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.url_safe.URLSafeSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URLSafeSerializerMixin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["itsdangerous.serializer.Serializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.url_safe.URLSafeSerializerMixin", "name": "URLSafeSerializerMixin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.url_safe.URLSafeSerializerMixin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.url_safe", "mro": ["itsdangerous.url_safe.URLSafeSerializerMixin", "itsdangerous.serializer.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "default_serializer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "itsdangerous.url_safe.URLSafeSerializerMixin.default_serializer", "name": "default_serializer", "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": ["itsdangerous._json._CompactJSON"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.type", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "itsdangerous._json._CompactJSON", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.url_safe.URLSafeSerializerMixin.dump_payload", "name": "dump_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["itsdangerous.url_safe.URLSafeSerializerMixin", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_payload of URLSafeSerializerMixin", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 4], "arg_names": ["self", "payload", "args", "serializer", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.url_safe.URLSafeSerializerMixin.load_payload", "name": "load_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 4], "arg_names": ["self", "payload", "args", "serializer", "kwargs"], "arg_types": ["itsdangerous.url_safe.URLSafeSerializerMixin", "builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_payload of URLSafeSerializerMixin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.url_safe.URLSafeSerializerMixin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.url_safe.URLSafeSerializerMixin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "URLSafeTimedSerializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["itsdangerous.url_safe.URLSafeSerializerMixin", "itsdangerous.timed.TimedSerializer"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.url_safe.URLSafeTimedSerializer", "name": "URLSafeTimedSerializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.url_safe.URLSafeTimedSerializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.url_safe", "mro": ["itsdangerous.url_safe.URLSafeTimedSerializer", "itsdangerous.url_safe.URLSafeSerializerMixin", "itsdangerous.timed.TimedSerializer", "itsdangerous.serializer.Serializer", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.url_safe.URLSafeTimedSerializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.url_safe.URLSafeTimedSerializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CompactJSON": {".class": "SymbolTableNode", "cross_ref": "itsdangerous._json._CompactJSON", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.url_safe.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.url_safe.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.url_safe.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.url_safe.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.url_safe.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.url_safe.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "base64_decode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_decode", "kind": "Gdef"}, "base64_encode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_encode", "kind": "Gdef"}, "zlib": {".class": "SymbolTableNode", "cross_ref": "zlib", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\itsdangerous\\url_safe.py"}
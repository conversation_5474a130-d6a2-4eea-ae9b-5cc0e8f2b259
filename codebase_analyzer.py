#!/usr/bin/env python3
"""
Advanced Codebase Analyzer: Build dynamic query enhancement patterns from actual codebase content.
Implements all 3 phases: Prototype, Integration, and Advanced Features.
"""

import re
import json
import requests
from collections import defaultdict, Counter
from typing import Dict, List, Set, Tuple, Any
import asyncio
from datetime import datetime
import hashlib

class CodebaseAnalyzer:
    """Advanced analyzer that builds dynamic enhancement patterns from codebase chunks"""

    def __init__(self, server_url: str = "http://home-ai-server.local:5002"):
        self.server_url = server_url
        self.patterns = {
            'functions': [],
            'domains': defaultdict(list),
            'keywords': set(),
            'prefixes': defaultdict(list),
            'types': set(),
            'constants': set(),
            'usage_frequency': defaultdict(int),
            'semantic_clusters': defaultdict(list),
            'cross_references': defaultdict(set),
            'enhancement_rules': {},
            'analysis_metadata': {
                'analyzed_at': None,
                'chunk_count': 0,
                'codebase_hash': None
            }
        }
    
    def analyze_chunks(self, chunks: List[Dict]) -> Dict:
        """Phase 1: Analyze all chunks to build enhancement patterns"""
        print(f"🔍 Phase 1: Analyzing {len(chunks)} chunks...")

        # Update metadata
        self.patterns['analysis_metadata']['analyzed_at'] = datetime.now().isoformat()
        self.patterns['analysis_metadata']['chunk_count'] = len(chunks)
        self.patterns['analysis_metadata']['codebase_hash'] = self._calculate_codebase_hash(chunks)

        for chunk in chunks:
            self._analyze_chunk(chunk)

        # Phase 1: Basic pattern building
        self._build_domain_patterns()
        self._build_enhancement_rules()

        # Phase 3: Advanced features
        self._build_semantic_clusters()
        self._analyze_cross_references()
        self._calculate_usage_frequency()

        print(f"✅ Analysis complete: {len(self.patterns['functions'])} functions, {len(self.patterns['domains'])} domains")
        return self.patterns

    def _calculate_codebase_hash(self, chunks: List[Dict]) -> str:
        """Calculate hash of codebase content for change detection"""
        content_hash = hashlib.md5()
        for chunk in chunks:
            content = chunk.get('content', '')
            content_hash.update(content.encode('utf-8'))
        return content_hash.hexdigest()[:16]
    
    def _analyze_chunk(self, chunk: Dict):
        """Analyze a single chunk"""
        content = chunk.get('content', '')
        metadata = chunk.get('metadata', {})
        
        # Extract function names
        functions = self._extract_functions(content)
        self.patterns['functions'].extend(functions)
        
        # Extract keywords and identifiers
        keywords = self._extract_keywords(content)
        self.patterns['keywords'].update(keywords)
        
        # Extract type definitions
        types = self._extract_types(content)
        self.patterns['types'].update(types)
        
        # Extract constants
        constants = self._extract_constants(content)
        self.patterns['constants'].update(constants)
        
        # Analyze semantic tags if available
        if 'semantic_tags' in metadata:
            self._analyze_semantic_tags(metadata['semantic_tags'], functions)
    
    def _extract_functions(self, content: str) -> List[str]:
        """Extract function names from code content"""
        functions = []
        
        # C/C++ function patterns
        patterns = [
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',  # Function calls
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*{',  # Function definitions
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            functions.extend(matches)
        
        # Filter out common keywords
        keywords_to_exclude = {
            'if', 'for', 'while', 'switch', 'return', 'sizeof', 'malloc', 'free',
            'printf', 'scanf', 'strlen', 'strcpy', 'strcmp', 'memcpy', 'memset'
        }
        
        functions = [f for f in functions if f not in keywords_to_exclude and len(f) > 2]
        return list(set(functions))  # Remove duplicates
    
    def _extract_keywords(self, content: str) -> Set[str]:
        """Extract relevant keywords and identifiers"""
        keywords = set()
        
        # Extract identifiers (variables, types, etc.)
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', content)
        
        # Filter for relevant keywords (length > 3, not common C keywords)
        common_keywords = {
            'void', 'char', 'int', 'long', 'short', 'float', 'double', 'const',
            'static', 'extern', 'auto', 'register', 'volatile', 'signed', 'unsigned',
            'struct', 'union', 'enum', 'typedef', 'sizeof', 'return', 'break',
            'continue', 'goto', 'case', 'default', 'switch', 'else', 'while',
            'for', 'do', 'if', 'NULL', 'true', 'false'
        }
        
        for identifier in identifiers:
            if len(identifier) > 3 and identifier.lower() not in common_keywords:
                keywords.add(identifier)
        
        return keywords
    
    def _extract_types(self, content: str) -> Set[str]:
        """Extract type definitions"""
        types = set()
        
        # Struct/enum/typedef patterns
        patterns = [
            r'typedef\s+struct\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'typedef\s+enum\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'typedef\s+[^{]+\s+([a-zA-Z_][a-zA-Z0-9_]*);',
            r'struct\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'enum\s+([a-zA-Z_][a-zA-Z0-9_]*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            types.update(matches)
        
        return types
    
    def _extract_constants(self, content: str) -> Set[str]:
        """Extract constant definitions"""
        constants = set()
        
        # #define patterns
        defines = re.findall(r'#define\s+([A-Z_][A-Z0-9_]*)', content)
        constants.update(defines)
        
        # Enum values
        enum_values = re.findall(r'\b([A-Z_][A-Z0-9_]*)\s*[,}]', content)
        constants.update(enum_values)
        
        return constants
    
    def _analyze_semantic_tags(self, tags: List[str], functions: List[str]):
        """Analyze semantic tags to build domain mappings"""
        for tag in tags:
            if tag in ['memory_management', 'error_handling', 'network_operations', 
                      'timer_operations', 'io_operations', 'configuration']:
                self.patterns['domains'][tag].extend(functions)
    
    def _build_domain_patterns(self):
        """Build domain patterns based on function prefixes"""
        prefix_domains = {
            'tmwmem': 'memory_management',
            'tmwerr': 'error_handling', 
            'tmwtimer': 'timer_operations',
            'tmwlink': 'network_operations',
            'tmwphys': 'network_operations',
            'tmwcnfg': 'configuration',
            'tmwcrypto': 'cryptography',
            'tmwsim': 'simulation'
        }
        
        for function in self.patterns['functions']:
            for prefix, domain in prefix_domains.items():
                if function.startswith(prefix):
                    self.patterns['domains'][domain].append(function)
                    self.patterns['prefixes'][prefix].append(function)
    
    def _build_enhancement_rules(self):
        """Build enhancement rules based on discovered patterns"""
        self.patterns['enhancement_rules'] = {}

        for domain, functions in self.patterns['domains'].items():
            if functions:
                # Take top 6 most common functions for this domain
                unique_functions = list(set(functions))[:6]
                self.patterns['enhancement_rules'][domain] = unique_functions

    def _build_semantic_clusters(self):
        """Phase 3: Build semantic clusters of related functions"""
        print("🧠 Building semantic clusters...")

        # Group functions by common patterns
        for function in self.patterns['functions']:
            # Cluster by prefix
            prefix = self._extract_prefix(function)
            if prefix:
                self.patterns['semantic_clusters'][f"prefix_{prefix}"].append(function)

            # Cluster by suffix patterns
            if function.endswith('_init') or function.endswith('Init'):
                self.patterns['semantic_clusters']['initialization'].append(function)
            elif function.endswith('_free') or function.endswith('_destroy'):
                self.patterns['semantic_clusters']['cleanup'].append(function)
            elif function.endswith('_callback') or function.endswith('Callback'):
                self.patterns['semantic_clusters']['callbacks'].append(function)

    def _analyze_cross_references(self):
        """Phase 3: Analyze cross-references between functions"""
        print("🔗 Analyzing cross-references...")

        # This would analyze which functions call which other functions
        # For now, we'll build basic relationships based on naming patterns
        for function in self.patterns['functions']:
            prefix = self._extract_prefix(function)
            if prefix:
                # Functions with same prefix are likely related
                related_functions = [f for f in self.patterns['functions']
                                   if f.startswith(prefix) and f != function]
                self.patterns['cross_references'][function].update(related_functions[:5])

    def _calculate_usage_frequency(self):
        """Phase 3: Calculate usage frequency weighting"""
        print("📊 Calculating usage frequency...")

        # Count occurrences of each function across all chunks
        function_counts = Counter(self.patterns['functions'])

        # Store frequency data
        for function, count in function_counts.items():
            self.patterns['usage_frequency'][function] = count

    def _extract_prefix(self, function_name: str) -> str:
        """Extract common prefix from function name"""
        # Look for common patterns like tmw*, *_
        if '_' in function_name:
            return function_name.split('_')[0]
        elif function_name.startswith('tmw'):
            # Extract tmw + next part
            match = re.match(r'(tmw[a-z]*)', function_name.lower())
            return match.group(1) if match else None
        return None
    
    def get_enhancement_for_query(self, query: str) -> List[str]:
        """Phase 3: Advanced query enhancement with frequency weighting and semantic clustering"""
        query_lower = query.lower()
        enhancements = []

        # Check domain mappings
        domain_mappings = {
            'memory': 'memory_management',
            'error': 'error_handling',
            'timer': 'timer_operations',
            'network': 'network_operations',
            'socket': 'network_operations',
            'config': 'configuration',
            'crypto': 'cryptography',
            'init': 'initialization',
            'cleanup': 'cleanup',
            'callback': 'callbacks'
        }

        # Primary domain matching
        for keyword, domain in domain_mappings.items():
            if keyword in query_lower:
                domain_functions = self.patterns['enhancement_rules'].get(domain, [])

                # Sort by usage frequency if available
                if self.patterns['usage_frequency']:
                    domain_functions = sorted(domain_functions,
                                            key=lambda f: self.patterns['usage_frequency'].get(f, 0),
                                            reverse=True)

                enhancements.extend(domain_functions[:4])  # Top 4 functions
                break

        # Semantic cluster matching
        if not enhancements:
            for cluster_name, cluster_functions in self.patterns['semantic_clusters'].items():
                if any(term in query_lower for term in cluster_name.split('_')):
                    enhancements.extend(cluster_functions[:3])
                    break

        # Cross-reference enhancement
        if enhancements:
            primary_function = enhancements[0]
            related_functions = list(self.patterns['cross_references'].get(primary_function, []))
            enhancements.extend(related_functions[:2])  # Add 2 related functions

        return list(set(enhancements))  # Remove duplicates
    
    def save_patterns(self, filename: str):
        """Save patterns to file"""
        # Convert sets to lists for JSON serialization
        serializable_patterns = {}
        for key, value in self.patterns.items():
            if isinstance(value, set):
                serializable_patterns[key] = list(value)
            elif isinstance(value, defaultdict):
                serializable_patterns[key] = dict(value)
            else:
                serializable_patterns[key] = value
        
        with open(filename, 'w') as f:
            json.dump(serializable_patterns, f, indent=2)
        
        print(f"💾 Patterns saved to {filename}")

    def load_patterns(self, filename: str) -> bool:
        """Load patterns from file"""
        try:
            with open(filename, 'r') as f:
                loaded_patterns = json.load(f)

            # Convert back to appropriate types
            for key, value in loaded_patterns.items():
                if key in ['keywords', 'types', 'constants']:
                    self.patterns[key] = set(value)
                elif key in ['domains', 'prefixes', 'semantic_clusters', 'cross_references', 'usage_frequency']:
                    self.patterns[key] = defaultdict(list if key != 'usage_frequency' else int, value)
                else:
                    self.patterns[key] = value

            print(f"📂 Patterns loaded from {filename}")
            return True
        except Exception as e:
            print(f"❌ Failed to load patterns: {e}")
            return False

    # Phase 2: RAG Server Integration
    async def fetch_codebase_chunks(self, codebase_name: str) -> List[Dict]:
        """Phase 2: Fetch all chunks from RAG server for analysis"""
        try:
            print(f"📡 Fetching chunks for codebase: {codebase_name}")

            # This would call the RAG server to get all chunks
            # For now, we'll simulate this
            response = requests.get(f"{self.server_url}/codebases/{codebase_name}/chunks")

            if response.status_code == 200:
                chunks = response.json().get('chunks', [])
                print(f"✅ Fetched {len(chunks)} chunks")
                return chunks
            else:
                print(f"❌ Failed to fetch chunks: {response.status_code}")
                return []

        except Exception as e:
            print(f"❌ Error fetching chunks: {e}")
            return []

    async def analyze_codebase_from_server(self, codebase_name: str) -> Dict:
        """Phase 2: Complete pipeline - fetch and analyze codebase from server"""
        print(f"🚀 Starting complete analysis for codebase: {codebase_name}")

        # Check if we have cached patterns
        cache_filename = f"{codebase_name}_patterns.json"
        if self.load_patterns(cache_filename):
            print("✅ Using cached patterns")
            return self.patterns

        # Fetch chunks from server
        chunks = await self.fetch_codebase_chunks(codebase_name)

        if not chunks:
            print("❌ No chunks available for analysis")
            return {}

        # Analyze chunks
        patterns = self.analyze_chunks(chunks)

        # Cache the results
        self.save_patterns(cache_filename)

        return patterns

# Example usage
if __name__ == "__main__":
    analyzer = CodebaseAnalyzer()
    
    # This would be called with actual chunks from the RAG server
    # For now, we'll create a simple test
    test_chunks = [
        {
            'content': '''
            void tmwmem_lowAlloc(TMWMEM_POOL *pPool, int size) {
                if (pPool != NULL) {
                    // Allocate memory
                }
            }
            
            int tmwerr_setError(int errorCode, const char* message) {
                return errorCode;
            }
            ''',
            'metadata': {
                'semantic_tags': ['memory_management', 'error_handling']
            }
        }
    ]
    
    patterns = analyzer.analyze_chunks(test_chunks)
    
    print("🔍 Discovered Patterns:")
    print(f"Functions: {patterns['functions']}")
    print(f"Domains: {dict(patterns['domains'])}")
    print(f"Enhancement Rules: {patterns.get('enhancement_rules', {})}")
    
    # Test enhancement
    test_query = "how is memory managed"
    enhancements = analyzer.get_enhancement_for_query(test_query)
    print(f"\nQuery: '{test_query}'")
    print(f"Enhancements: {enhancements}")

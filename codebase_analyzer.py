#!/usr/bin/env python3
"""
Prototype: Analyze codebase chunks to build dynamic query enhancement patterns.
"""

import re
import json
from collections import defaultdict
from typing import Dict, List, Set

class CodebaseAnalyzer:
    """Analyze codebase chunks to build dynamic enhancement patterns"""
    
    def __init__(self):
        self.patterns = {
            'functions': [],
            'domains': defaultdict(list),
            'keywords': set(),
            'prefixes': defaultdict(list),
            'types': set(),
            'constants': set()
        }
    
    def analyze_chunks(self, chunks: List[Dict]) -> Dict:
        """Analyze all chunks to build enhancement patterns"""
        print(f"🔍 Analyzing {len(chunks)} chunks...")
        
        for chunk in chunks:
            self._analyze_chunk(chunk)
        
        # Post-process patterns
        self._build_domain_patterns()
        self._build_enhancement_rules()
        
        return self.patterns
    
    def _analyze_chunk(self, chunk: Dict):
        """Analyze a single chunk"""
        content = chunk.get('content', '')
        metadata = chunk.get('metadata', {})
        
        # Extract function names
        functions = self._extract_functions(content)
        self.patterns['functions'].extend(functions)
        
        # Extract keywords and identifiers
        keywords = self._extract_keywords(content)
        self.patterns['keywords'].update(keywords)
        
        # Extract type definitions
        types = self._extract_types(content)
        self.patterns['types'].update(types)
        
        # Extract constants
        constants = self._extract_constants(content)
        self.patterns['constants'].update(constants)
        
        # Analyze semantic tags if available
        if 'semantic_tags' in metadata:
            self._analyze_semantic_tags(metadata['semantic_tags'], functions)
    
    def _extract_functions(self, content: str) -> List[str]:
        """Extract function names from code content"""
        functions = []
        
        # C/C++ function patterns
        patterns = [
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',  # Function calls
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*{',  # Function definitions
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            functions.extend(matches)
        
        # Filter out common keywords
        keywords_to_exclude = {
            'if', 'for', 'while', 'switch', 'return', 'sizeof', 'malloc', 'free',
            'printf', 'scanf', 'strlen', 'strcpy', 'strcmp', 'memcpy', 'memset'
        }
        
        functions = [f for f in functions if f not in keywords_to_exclude and len(f) > 2]
        return list(set(functions))  # Remove duplicates
    
    def _extract_keywords(self, content: str) -> Set[str]:
        """Extract relevant keywords and identifiers"""
        keywords = set()
        
        # Extract identifiers (variables, types, etc.)
        identifiers = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', content)
        
        # Filter for relevant keywords (length > 3, not common C keywords)
        common_keywords = {
            'void', 'char', 'int', 'long', 'short', 'float', 'double', 'const',
            'static', 'extern', 'auto', 'register', 'volatile', 'signed', 'unsigned',
            'struct', 'union', 'enum', 'typedef', 'sizeof', 'return', 'break',
            'continue', 'goto', 'case', 'default', 'switch', 'else', 'while',
            'for', 'do', 'if', 'NULL', 'true', 'false'
        }
        
        for identifier in identifiers:
            if len(identifier) > 3 and identifier.lower() not in common_keywords:
                keywords.add(identifier)
        
        return keywords
    
    def _extract_types(self, content: str) -> Set[str]:
        """Extract type definitions"""
        types = set()
        
        # Struct/enum/typedef patterns
        patterns = [
            r'typedef\s+struct\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'typedef\s+enum\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'typedef\s+[^{]+\s+([a-zA-Z_][a-zA-Z0-9_]*);',
            r'struct\s+([a-zA-Z_][a-zA-Z0-9_]*)',
            r'enum\s+([a-zA-Z_][a-zA-Z0-9_]*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content)
            types.update(matches)
        
        return types
    
    def _extract_constants(self, content: str) -> Set[str]:
        """Extract constant definitions"""
        constants = set()
        
        # #define patterns
        defines = re.findall(r'#define\s+([A-Z_][A-Z0-9_]*)', content)
        constants.update(defines)
        
        # Enum values
        enum_values = re.findall(r'\b([A-Z_][A-Z0-9_]*)\s*[,}]', content)
        constants.update(enum_values)
        
        return constants
    
    def _analyze_semantic_tags(self, tags: List[str], functions: List[str]):
        """Analyze semantic tags to build domain mappings"""
        for tag in tags:
            if tag in ['memory_management', 'error_handling', 'network_operations', 
                      'timer_operations', 'io_operations', 'configuration']:
                self.patterns['domains'][tag].extend(functions)
    
    def _build_domain_patterns(self):
        """Build domain patterns based on function prefixes"""
        prefix_domains = {
            'tmwmem': 'memory_management',
            'tmwerr': 'error_handling', 
            'tmwtimer': 'timer_operations',
            'tmwlink': 'network_operations',
            'tmwphys': 'network_operations',
            'tmwcnfg': 'configuration',
            'tmwcrypto': 'cryptography',
            'tmwsim': 'simulation'
        }
        
        for function in self.patterns['functions']:
            for prefix, domain in prefix_domains.items():
                if function.startswith(prefix):
                    self.patterns['domains'][domain].append(function)
                    self.patterns['prefixes'][prefix].append(function)
    
    def _build_enhancement_rules(self):
        """Build enhancement rules based on discovered patterns"""
        self.patterns['enhancement_rules'] = {}
        
        for domain, functions in self.patterns['domains'].items():
            if functions:
                # Take top 6 most common functions for this domain
                unique_functions = list(set(functions))[:6]
                self.patterns['enhancement_rules'][domain] = unique_functions
    
    def get_enhancement_for_query(self, query: str) -> List[str]:
        """Get enhancement terms for a specific query"""
        query_lower = query.lower()
        enhancements = []
        
        # Check domain mappings
        domain_mappings = {
            'memory': 'memory_management',
            'error': 'error_handling',
            'timer': 'timer_operations',
            'network': 'network_operations',
            'socket': 'network_operations',
            'config': 'configuration',
            'crypto': 'cryptography'
        }
        
        for keyword, domain in domain_mappings.items():
            if keyword in query_lower:
                domain_functions = self.patterns['enhancement_rules'].get(domain, [])
                enhancements.extend(domain_functions[:4])  # Top 4 functions
                break
        
        return enhancements
    
    def save_patterns(self, filename: str):
        """Save patterns to file"""
        # Convert sets to lists for JSON serialization
        serializable_patterns = {}
        for key, value in self.patterns.items():
            if isinstance(value, set):
                serializable_patterns[key] = list(value)
            elif isinstance(value, defaultdict):
                serializable_patterns[key] = dict(value)
            else:
                serializable_patterns[key] = value
        
        with open(filename, 'w') as f:
            json.dump(serializable_patterns, f, indent=2)
        
        print(f"💾 Patterns saved to {filename}")

# Example usage
if __name__ == "__main__":
    analyzer = CodebaseAnalyzer()
    
    # This would be called with actual chunks from the RAG server
    # For now, we'll create a simple test
    test_chunks = [
        {
            'content': '''
            void tmwmem_lowAlloc(TMWMEM_POOL *pPool, int size) {
                if (pPool != NULL) {
                    // Allocate memory
                }
            }
            
            int tmwerr_setError(int errorCode, const char* message) {
                return errorCode;
            }
            ''',
            'metadata': {
                'semantic_tags': ['memory_management', 'error_handling']
            }
        }
    ]
    
    patterns = analyzer.analyze_chunks(test_chunks)
    
    print("🔍 Discovered Patterns:")
    print(f"Functions: {patterns['functions']}")
    print(f"Domains: {dict(patterns['domains'])}")
    print(f"Enhancement Rules: {patterns.get('enhancement_rules', {})}")
    
    # Test enhancement
    test_query = "how is memory managed"
    enhancements = analyzer.get_enhancement_for_query(test_query)
    print(f"\nQuery: '{test_query}'")
    print(f"Enhancements: {enhancements}")

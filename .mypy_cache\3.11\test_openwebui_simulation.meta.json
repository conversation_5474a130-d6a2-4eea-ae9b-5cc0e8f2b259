{"data_mtime": 1751390589, "dep_lines": [6, 7, 8, 13, 86, 54, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["asyncio", "sys", "pathlib", "open_webui_code_analyzer_tool", "traceback", "re", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "os", "typing", "typing_extensions"], "hash": "fb4cac3288c8431d917235c9acb5a55437bb9a5e", "id": "test_openwebui_simulation", "ignore_all": false, "interface_hash": "5eacf9d0ade70d8b67c875729de21c4212d6cff2", "mtime": 1751390587, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_openwebui_simulation.py", "plugin_data": null, "size": 4388, "suppressed": [], "version_id": "1.15.0"}
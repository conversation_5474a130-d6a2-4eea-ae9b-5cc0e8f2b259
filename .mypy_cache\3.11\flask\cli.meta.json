{"data_mtime": 1751398266, "dep_lines": [4, 5, 18, 20, 21, 23, 24, 30, 34, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 17, 19, 28, 794, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 25, 25, 5, 10, 20, 20, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["collections.abc", "importlib.metadata", "click.core", "werkzeug.serving", "werkzeug.utils", "flask.globals", "flask.helpers", "_typeshed.wsgi", "flask.app", "__future__", "ast", "collections", "importlib", "inspect", "os", "platform", "re", "sys", "traceback", "typing", "functools", "operator", "types", "click", "werkzeug", "ssl", "cryptography", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "click.exceptions", "click.types", "enum", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "typing_extensions"], "hash": "39fc06a17692e8e311b7e0f9a028a074bed3a21e", "id": "flask.cli", "ignore_all": true, "interface_hash": "dcaecc02770ad8c63b89e146c8263036510d5063", "mtime": 1708667823, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\cli.py", "plugin_data": null, "size": 35833, "suppressed": [], "version_id": "1.15.0"}
#!/usr/bin/env python3
"""
Test the improved generic query enhancement.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

async def test_generic_enhancement():
    """Test the generic query enhancement system"""
    print("🔍 Testing Generic Query Enhancement System")
    print("=" * 60)
    
    # Initialize the tool
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Clear cache to test fresh
    if tool.cache:
        await tool.clear_cache()
    
    # Ensure utils is selected
    print("1. Selecting utils codebase...")
    await tool.select_codebase("utils")
    
    # Test various query types that should now work
    test_queries = [
        # Memory queries
        "how is memory managed",
        "show me memory allocation",
        
        # Error queries  
        "how are errors handled",
        "find error reporting",
        
        # Network queries (new)
        "show me network code",
        "how does socket work",
        
        # Timer queries (new)
        "find timer functions",
        "how are timers managed",
        
        # Config queries (new)
        "show me configuration",
        "how are settings handled",
        
        # Generic queries
        "show me functions",
        "find code",
        "explain implementation"
    ]
    
    successful = 0
    failed = 0
    
    for i, query in enumerate(test_queries, 2):
        print(f"\n{i}. Testing: '{query}'")
        try:
            result = await tool.__call__(query)
            
            if "❌ Unable to retrieve code context" in result or "Context retrieval failed" in result:
                print(f"   ❌ FAILED: Context retrieval failed")
                failed += 1
            elif len(result) > 500:
                print(f"   ✅ SUCCESS: Got {len(result)} characters")
                successful += 1
                
                # Check for relevant content
                if any(term in result.lower() for term in ['tmw', 'function', 'code', query.split()[0].lower()]):
                    print(f"   ✅ Contains relevant content")
                else:
                    print(f"   ⚠️ Content might be generic")
            else:
                print(f"   ⚠️ SHORT: {len(result)} characters")
                print(f"   Content: {result[:150]}...")
                failed += 1
                
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
            failed += 1
    
    print(f"\n" + "=" * 60)
    print(f"📊 RESULTS: {successful} successful, {failed} failed")
    print(f"Success rate: {successful/(successful+failed)*100:.1f}%")
    
    if successful > failed:
        print("🎉 Generic enhancement is working well!")
    else:
        print("⚠️ Generic enhancement needs more work")
    
    print("🎉 Generic enhancement test completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_generic_enhancement())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)

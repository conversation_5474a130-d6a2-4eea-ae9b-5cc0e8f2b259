#!/usr/bin/env python3
"""
RAG Server Enhancement: Add dynamic codebase analysis endpoints.
This would be integrated into the main RAG server.
"""

from flask import Flask, request, jsonify
import json
import os
from codebase_analyzer import Codebase<PERSON><PERSON>yzer
from typing import Dict, List

class RAGServerEnhancement:
    """Enhancement to RAG server for dynamic codebase analysis"""
    
    def __init__(self, app: Flask):
        self.app = app
        self.analyzer = CodebaseAnalyzer()
        self.patterns_cache = {}  # In-memory cache for patterns
        self.setup_routes()
    
    def setup_routes(self):
        """Setup new routes for codebase analysis"""
        
        @self.app.route('/analyze_codebase/<codebase_name>', methods=['POST'])
        def analyze_codebase(codebase_name: str):
            """Analyze a codebase and build enhancement patterns"""
            try:
                # This would fetch chunks from the vector database
                chunks = self._fetch_codebase_chunks(codebase_name)
                
                if not chunks:
                    return jsonify({
                        "error": f"No chunks found for codebase: {codebase_name}"
                    }), 404
                
                # Analyze chunks
                patterns = self.analyzer.analyze_chunks(chunks)
                
                # Cache patterns
                self.patterns_cache[codebase_name] = patterns
                
                # Save to disk
                cache_filename = f"{codebase_name}_patterns.json"
                self.analyzer.save_patterns(cache_filename)
                
                return jsonify({
                    "result": "Analysis complete",
                    "codebase": codebase_name,
                    "functions_discovered": len(patterns.get('functions', [])),
                    "domains_identified": list(patterns.get('domains', {}).keys()),
                    "enhancement_rules": len(patterns.get('enhancement_rules', {})),
                    "patterns_cached": True
                })
                
            except Exception as e:
                return jsonify({
                    "error": f"Analysis failed: {str(e)}"
                }), 500
        
        @self.app.route('/enhancement_patterns/<codebase_name>', methods=['GET'])
        def get_enhancement_patterns(codebase_name: str):
            """Get enhancement patterns for a codebase"""
            try:
                # Check cache first
                if codebase_name in self.patterns_cache:
                    patterns = self.patterns_cache[codebase_name]
                else:
                    # Try to load from disk
                    cache_filename = f"{codebase_name}_patterns.json"
                    if self.analyzer.load_patterns(cache_filename):
                        patterns = self.analyzer.patterns
                        self.patterns_cache[codebase_name] = patterns
                    else:
                        return jsonify({
                            "error": f"No patterns found for codebase: {codebase_name}. Run analysis first."
                        }), 404
                
                # Convert sets to lists for JSON serialization
                serializable_patterns = self._serialize_patterns(patterns)
                
                return jsonify({
                    "codebase": codebase_name,
                    "patterns": serializable_patterns
                })
                
            except Exception as e:
                return jsonify({
                    "error": f"Failed to get patterns: {str(e)}"
                }), 500
        
        @self.app.route('/enhance_query', methods=['POST'])
        def enhance_query():
            """Enhance a query using dynamic patterns"""
            try:
                data = request.get_json()
                query = data.get('query', '')
                codebase_name = data.get('codebase_name', '')
                
                if not query or not codebase_name:
                    return jsonify({
                        "error": "Query and codebase_name are required"
                    }), 400
                
                # Get patterns for codebase
                if codebase_name not in self.patterns_cache:
                    cache_filename = f"{codebase_name}_patterns.json"
                    if not self.analyzer.load_patterns(cache_filename):
                        return jsonify({
                            "error": f"No patterns found for codebase: {codebase_name}"
                        }), 404
                    self.patterns_cache[codebase_name] = self.analyzer.patterns
                
                # Get enhancements
                enhancements = self.analyzer.get_enhancement_for_query(query)
                
                # Build enhanced query
                enhanced_query = f"{query} {' '.join(enhancements)}" if enhancements else query
                
                return jsonify({
                    "original_query": query,
                    "enhanced_query": enhanced_query,
                    "enhancements": enhancements,
                    "codebase": codebase_name
                })
                
            except Exception as e:
                return jsonify({
                    "error": f"Enhancement failed: {str(e)}"
                }), 500
        
        @self.app.route('/codebase_stats/<codebase_name>', methods=['GET'])
        def get_codebase_stats(codebase_name: str):
            """Get statistics about analyzed codebase"""
            try:
                if codebase_name not in self.patterns_cache:
                    cache_filename = f"{codebase_name}_patterns.json"
                    if not self.analyzer.load_patterns(cache_filename):
                        return jsonify({
                            "error": f"No analysis found for codebase: {codebase_name}"
                        }), 404
                    self.patterns_cache[codebase_name] = self.analyzer.patterns
                
                patterns = self.patterns_cache[codebase_name]
                
                stats = {
                    "codebase": codebase_name,
                    "analysis_metadata": patterns.get('analysis_metadata', {}),
                    "functions_count": len(patterns.get('functions', [])),
                    "domains_count": len(patterns.get('domains', {})),
                    "keywords_count": len(patterns.get('keywords', set())),
                    "types_count": len(patterns.get('types', set())),
                    "constants_count": len(patterns.get('constants', set())),
                    "semantic_clusters_count": len(patterns.get('semantic_clusters', {})),
                    "cross_references_count": len(patterns.get('cross_references', {})),
                    "enhancement_rules_count": len(patterns.get('enhancement_rules', {}))
                }
                
                return jsonify(stats)
                
            except Exception as e:
                return jsonify({
                    "error": f"Failed to get stats: {str(e)}"
                }), 500
    
    def _fetch_codebase_chunks(self, codebase_name: str) -> List[Dict]:
        """Fetch all chunks for a codebase from vector database"""
        # This would integrate with the existing vector database
        # For now, return empty list (would be implemented in actual server)
        return []
    
    def _serialize_patterns(self, patterns: Dict) -> Dict:
        """Convert patterns to JSON-serializable format"""
        serializable = {}
        
        for key, value in patterns.items():
            if isinstance(value, set):
                serializable[key] = list(value)
            elif hasattr(value, 'items'):  # dict-like
                serializable[key] = dict(value)
            else:
                serializable[key] = value
        
        return serializable

# Example integration with existing Flask app
def integrate_with_rag_server(app: Flask):
    """Integrate dynamic analysis with existing RAG server"""
    enhancement = RAGServerEnhancement(app)
    return enhancement

# Standalone test server
if __name__ == "__main__":
    app = Flask(__name__)
    
    # Add basic health check
    @app.route('/health', methods=['GET'])
    def health():
        return jsonify({"status": "healthy", "service": "RAG Server Enhancement"})
    
    # Integrate enhancement
    enhancement = integrate_with_rag_server(app)
    
    print("🚀 RAG Server Enhancement running on http://localhost:5003")
    print("📋 Available endpoints:")
    print("   POST /analyze_codebase/<name> - Analyze codebase")
    print("   GET  /enhancement_patterns/<name> - Get patterns")
    print("   POST /enhance_query - Enhance query")
    print("   GET  /codebase_stats/<name> - Get statistics")
    
    app.run(host='0.0.0.0', port=5003, debug=True)

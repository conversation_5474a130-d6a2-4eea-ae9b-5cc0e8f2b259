{".class": "MypyFile", "_fullname": "click.testing", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BytesIOCopy": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["_io.BytesIO"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.testing.BytesIOCopy", "name": "BytesIOCopy", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.testing.BytesIOCopy", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "click.testing", "mro": ["click.testing.BytesIOCopy", "_io.BytesIO", "io.BufferedIOBase", "_io._BufferedIOBase", "io.IOBase", "_io._IOBase", "typing.BinaryIO", "typing.IO", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "copy_to"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.BytesIOCopy.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "copy_to"], "arg_types": ["click.testing.BytesIOCopy", "_io.BytesIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of BytesIOCopy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "copy_to": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.BytesIOCopy.copy_to", "name": "copy_to", "type": "_io.BytesIO"}}, "flush": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.BytesIOCopy.flush", "name": "flush", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.BytesIOCopy"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "flush of BytesIOCopy", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "b"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.BytesIOCopy.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "b"], "arg_types": ["click.testing.BytesIOCopy", "typing_extensions.Buffer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of BytesIOCopy", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.testing.BytesIOCopy.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.testing.BytesIOCopy", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CliRunner": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.testing.CliRunner", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.testing.CliRunner", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.testing", "mro": ["click.testing.CliRunner", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "charset", "env", "echo_stdin", "catch_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.CliRunner.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1], "arg_names": ["self", "charset", "env", "echo_stdin", "catch_exceptions"], "arg_types": ["click.testing.CliRunner", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "catch_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.CliRunner.catch_exceptions", "name": "catch_exceptions", "type": "builtins.bool"}}, "charset": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.CliRunner.charset", "name": "charset", "type": "builtins.str"}}, "echo_stdin": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.CliRunner.echo_stdin", "name": "echo_stdin", "type": "builtins.bool"}}, "env": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.testing.CliRunner.env", "name": "env", "type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "get_default_prog_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "cli"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.CliRunner.get_default_prog_name", "name": "get_default_prog_name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "cli"], "arg_types": ["click.testing.CliRunner", "click.core.Command"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_default_prog_name of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invoke": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "cli", "args", "input", "env", "catch_exceptions", "color", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.CliRunner.invoke", "name": "invoke", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 4], "arg_names": ["self", "cli", "args", "input", "env", "catch_exceptions", "color", "extra"], "arg_types": ["click.testing.CliRunner", "click.core.Command", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invoke of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "click.testing.Result", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "isolated_filesystem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "click.testing.CliRunner.isolated_filesystem", "name": "isolated_filesystem", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "temp_dir"], "arg_types": ["click.testing.CliRunner", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isolated_filesystem of CliRunner", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "click.testing.CliRunner.isolated_filesystem", "name": "isolated_filesystem", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "temp_dir"], "arg_types": ["click.testing.CliRunner", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "os.PathLike"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isolated_filesystem of CliRunner", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "isolation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "input", "env", "color"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_decorated"], "fullname": "click.testing.CliRunner.isolation", "name": "isolation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "input", "env", "color"], "arg_types": ["click.testing.CliRunner", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isolation of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["_io.BytesIO", "_io.BytesIO", "_io.BytesIO"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "click.testing.CliRunner.isolation", "name": "isolation", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "input", "env", "color"], "arg_types": ["click.testing.CliRunner", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "isolation of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["_io.BytesIO", "_io.BytesIO", "_io.BytesIO"], "partial_fallback": {".class": "Instance", "args": ["_io.BytesIO"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "make_env": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "overrides"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.CliRunner.make_env", "name": "make_env", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "overrides"], "arg_types": ["click.testing.CliRunner", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_env of CliRunner", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Mapping"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.testing.CliRunner.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.testing.CliRunner", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Command": {".class": "SymbolTableNode", "cross_ref": "click.core.Command", "kind": "Gdef"}, "EchoingStdin": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.testing.EchoingStdin", "name": "EchoingStdin", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.testing", "mro": ["click.testing.EchoingStdin", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["click.testing.EchoingStdin", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__getattr__ of EchoingStdin", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input", "output"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input", "output"], "arg_types": ["click.testing.EchoingStdin", "typing.BinaryIO", "typing.BinaryIO"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of EchoingStdin", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["click.testing.EchoingStdin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__iter__ of EchoingStdin", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["click.testing.EchoingStdin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of EchoingStdin", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_echo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "rv"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin._echo", "name": "_echo", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "rv"], "arg_types": ["click.testing.EchoingStdin", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_echo of EchoingStdin", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_input": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.EchoingStdin._input", "name": "_input", "type": "typing.BinaryIO"}}, "_output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.EchoingStdin._output", "name": "_output", "type": "typing.BinaryIO"}}, "_paused": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.EchoingStdin._paused", "name": "_paused", "type": "builtins.bool"}}, "read": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.read", "name": "read", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "n"], "arg_types": ["click.testing.EchoingStdin", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read of EchoingStdin", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "read1": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.read1", "name": "read1", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "n"], "arg_types": ["click.testing.EchoingStdin", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "read1 of EchoingStdin", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "n"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.readline", "name": "readline", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "n"], "arg_types": ["click.testing.EchoingStdin", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readline of EchoingStdin", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "readlines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.EchoingStdin.readlines", "name": "readlines", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.EchoingStdin"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "readlines of EchoingStdin", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.testing.EchoingStdin.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.testing.EchoingStdin", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ReadableBuffer": {".class": "SymbolTableNode", "cross_ref": "_typeshed.ReadableBuffer", "kind": "Gdef"}, "Result": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.testing.Result", "name": "Result", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.testing.Result", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.testing", "mro": ["click.testing.Result", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "runner", "stdout_bytes", "stderr_bytes", "output_bytes", "return_value", "exit_code", "exception", "exc_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.Result.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 1], "arg_names": ["self", "runner", "stdout_bytes", "stderr_bytes", "output_bytes", "return_value", "exit_code", "exception", "exc_info"], "arg_types": ["click.testing.Result", "click.testing.CliRunner", "builtins.bytes", "builtins.bytes", "builtins.bytes", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.BaseException"}, "builtins.BaseException", "types.TracebackType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Result", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.Result.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["click.testing.Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of Result", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "exc_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.exc_info", "name": "exc_info", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": "builtins.BaseException"}, "builtins.BaseException", "types.TracebackType"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "exception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.exception", "name": "exception", "type": {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "exit_code": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.exit_code", "name": "exit_code", "type": "builtins.int"}}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "click.testing.Result.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of Result", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "click.testing.Result.output", "name": "output", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "output of Result", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "output_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.output_bytes", "name": "output_bytes", "type": "builtins.bytes"}}, "return_value": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.return_value", "name": "return_value", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "runner": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.runner", "name": "runner", "type": "click.testing.CliRunner"}}, "stderr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "click.testing.Result.stderr", "name": "stderr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "st<PERSON><PERSON> of Result", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "click.testing.Result.stderr", "name": "stderr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "st<PERSON><PERSON> of Result", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stderr_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.stderr_bytes", "name": "stderr_bytes", "type": "builtins.bytes"}}, "stdout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "click.testing.Result.stdout", "name": "stdout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stdout of Result", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "click.testing.Result.stdout", "name": "stdout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.Result"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "stdout of Result", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "stdout_bytes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing.Result.stdout_bytes", "name": "stdout_bytes", "type": "builtins.bytes"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.testing.Result.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.testing.Result", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StreamMixer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.testing.StreamMixer", "name": "StreamMixer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.testing.StreamMixer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "click.testing", "mro": ["click.testing.StreamMixer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.StreamMixer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing.StreamMixer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StreamMixer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "output": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.testing.StreamMixer.output", "name": "output", "type": "_io.BytesIO"}}, "stderr": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.testing.StreamMixer.stderr", "name": "stderr", "type": "_io.BytesIO"}}, "stdout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "click.testing.StreamMixer.stdout", "name": "stdout", "type": "_io.BytesIO"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.testing.StreamMixer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.testing.StreamMixer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "_NamedTextIOWrapper": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["_io._W<PERSON><PERSON><PERSON>er"], "extra_attrs": null, "type_ref": "_io.TextIOWrapper"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "click.testing._NamedTextIOWrapper", "name": "_NamedTextIOWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "click.testing._NamedTextIOWrapper", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "click.testing", "mro": ["click.testing._NamedTextIOWrapper", "_io.TextIOWrapper", "io.TextIOBase", "_io._TextIOBase", "io.IOBase", "_io._IOBase", "typing.TextIO", "typing.IO", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "buffer", "name", "mode", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing._NamedTextIOWrapper.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 4], "arg_names": ["self", "buffer", "name", "mode", "kwargs"], "arg_types": ["click.testing._NamedTextIOWrapper", "typing.BinaryIO", "builtins.str", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _NamedTextIOWrapper", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing._NamedTextIOWrapper.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing._NamedTextIOWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__next__ of _NamedTextIOWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_mode": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing._NamedTextIOWrapper._mode", "name": "_mode", "type": "builtins.str"}}, "_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "click.testing._NamedTextIOWrapper._name", "name": "_name", "type": "builtins.str"}}, "mode": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "click.testing._NamedTextIOWrapper.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing._NamedTextIOWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of _NamedTextIOWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "click.testing._NamedTextIOWrapper.mode", "name": "mode", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing._NamedTextIOWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mode of _NamedTextIOWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "click.testing._NamedTextIOWrapper.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing._NamedTextIOWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of _NamedTextIOWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "click.testing._NamedTextIOWrapper.name", "name": "name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["click.testing._NamedTextIOWrapper"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "name of _NamedTextIOWrapper", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "click.testing._NamedTextIOWrapper.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "click.testing._NamedTextIOWrapper", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.testing.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.testing.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.testing.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.testing.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.testing.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "click.testing.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_compat": {".class": "SymbolTableNode", "cross_ref": "click._compat", "kind": "Gdef"}, "_find_binary_reader": {".class": "SymbolTableNode", "cross_ref": "click._compat._find_binary_reader", "kind": "Gdef"}, "_pause_echo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "click.testing._pause_echo", "name": "_pause_echo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["stream"], "arg_types": [{".class": "UnionType", "items": ["click.testing.EchoingStdin", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pause_echo", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "click.testing._pause_echo", "name": "_pause_echo", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["stream"], "arg_types": [{".class": "UnionType", "items": ["click.testing.EchoingStdin", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_pause_echo", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cabc": {".class": "SymbolTableNode", "cross_ref": "collections.abc", "kind": "Gdef"}, "contextlib": {".class": "SymbolTableNode", "cross_ref": "contextlib", "kind": "Gdef"}, "formatting": {".class": "SymbolTableNode", "cross_ref": "click.formatting", "kind": "Gdef"}, "io": {".class": "SymbolTableNode", "cross_ref": "io", "kind": "Gdef"}, "make_input_stream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["input", "charset"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "click.testing.make_input_stream", "name": "make_input_stream", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["input", "charset"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "builtins.bytes", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_input_stream", "ret_type": "typing.BinaryIO", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "shlex": {".class": "SymbolTableNode", "cross_ref": "shlex", "kind": "Gdef"}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "tempfile": {".class": "SymbolTableNode", "cross_ref": "tempfile", "kind": "Gdef"}, "termui": {".class": "SymbolTableNode", "cross_ref": "click.termui", "kind": "Gdef"}, "utils": {".class": "SymbolTableNode", "cross_ref": "click.utils", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\click\\testing.py"}
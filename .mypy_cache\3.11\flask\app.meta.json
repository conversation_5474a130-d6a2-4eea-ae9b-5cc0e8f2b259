{"data_mtime": 1751398266, "dep_lines": [43, 44, 3, 12, 15, 17, 20, 25, 26, 28, 29, 30, 32, 39, 45, 47, 52, 53, 57, 60, 455, 948, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 28, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 5, 5, 5, 5, 5, 25, 20, 20, 20, 5, 20, 10, 10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["flask.sansio.app", "flask.sansio.scaffold", "collections.abc", "urllib.parse", "werkzeug.datastructures", "werkzeug.exceptions", "werkzeug.routing", "werkzeug.serving", "werkzeug.wrappers", "flask.cli", "flask.typing", "flask.ctx", "flask.globals", "flask.helpers", "flask.sessions", "flask.signals", "flask.templating", "flask.wrappers", "_typeshed.wsgi", "flask.testing", "flask.debughelpers", "asgiref.sync", "__future__", "collections", "os", "sys", "typing", "weakref", "datetime", "inspect", "itertools", "types", "click", "flask", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "blinker", "blinker.base", "click.testing", "enum", "flask.config", "flask.json", "flask.json.provider", "flask.sansio", "http", "http.server", "jinja2", "jinja2.environment", "socketserver", "ssl", "typing_extensions", "werkzeug", "werkzeug.datastructures.headers", "werkzeug.datastructures.mixins", "werkzeug.datastructures.structures", "werkzeug.routing.map", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.test", "werkzeug.wrappers.request", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "940aafa4cb24b5be98adacfb1437d248707ccc61", "id": "flask.app", "ignore_all": true, "interface_hash": "7dfa6bf1fa4e60a8e28e2650b9894dd9e189b31d", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\app.py", "plugin_data": null, "size": 59706, "suppressed": [], "version_id": "1.15.0"}
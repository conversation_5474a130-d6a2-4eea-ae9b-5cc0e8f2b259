{".class": "MypyFile", "_fullname": "itsdangerous.signer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadSignature": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadSignature", "kind": "Gdef"}, "HMACAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["itsdangerous.signer.SigningAlgorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.signer.HMACAlgorithm", "name": "HMACAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.HMACAlgorithm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.signer", "mro": ["itsdangerous.signer.HMACAlgorithm", "itsdangerous.signer.SigningAlgorithm", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "digest_method"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.HMACAlgorithm.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "digest_method"], "arg_types": ["itsdangerous.signer.HMACAlgorithm", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of HMACAlgorithm", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_digest_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "itsdangerous.signer.HMACAlgorithm.default_digest_method", "name": "default_digest_method", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "digest_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.signer.HMACAlgorithm.digest_method", "name": "digest_method", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.HMACAlgorithm.get_signature", "name": "get_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["itsdangerous.signer.HMACAlgorithm", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_signature of HMACAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.signer.HMACAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.signer.HMACAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "NoneAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["itsdangerous.signer.SigningAlgorithm"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.signer.NoneAlgorithm", "name": "NoneAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.NoneAlgorithm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.signer", "mro": ["itsdangerous.signer.NoneAlgorithm", "itsdangerous.signer.SigningAlgorithm", "builtins.object"], "names": {".class": "SymbolTable", "get_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.NoneAlgorithm.get_signature", "name": "get_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["itsdangerous.signer.NoneAlgorithm", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_signature of NoneAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.signer.NoneAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.signer.NoneAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Signer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.signer.Signer", "name": "Signer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.signer", "mro": ["itsdangerous.signer.Signer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_key", "salt", "sep", "key_derivation", "digest_method", "algorithm"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_key", "salt", "sep", "key_derivation", "digest_method", "algorithm"], "arg_types": ["itsdangerous.signer.Signer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_secret_key"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_opt_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["itsdangerous.signer.SigningAlgorithm", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Signer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "algorithm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.signer.Signer.algorithm", "name": "algorithm", "type": "itsdangerous.signer.SigningAlgorithm"}}, "default_digest_method": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "itsdangerous.signer.Signer.default_digest_method", "name": "default_digest_method", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "default_key_derivation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "itsdangerous.signer.Signer.default_key_derivation", "name": "default_key_derivation", "type": "builtins.str"}}, "derive_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "secret_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer.derive_key", "name": "derive_key", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "secret_key"], "arg_types": ["itsdangerous.signer.Signer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "derive_key of Signer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "digest_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.signer.Signer.digest_method", "name": "digest_method", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "get_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer.get_signature", "name": "get_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["itsdangerous.signer.Signer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_signature of Signer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "key_derivation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.signer.Signer.key_derivation", "name": "key_derivation", "type": "builtins.str"}}, "salt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "itsdangerous.signer.Signer.salt", "name": "salt", "type": "builtins.bytes"}}, "secret_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "itsdangerous.signer.Signer.secret_key", "name": "secret_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["itsdangerous.signer.Signer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "secret_key of Signer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "itsdangerous.signer.Signer.secret_key", "name": "secret_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["itsdangerous.signer.Signer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "secret_key of Signer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "secret_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.signer.Signer.secret_keys", "name": "secret_keys", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "sep": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.signer.Signer.sep", "name": "sep", "type": "builtins.bytes"}}, "sign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer.sign", "name": "sign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "value"], "arg_types": ["itsdangerous.signer.Signer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "sign of Signer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "unsign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "signed_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer.unsign", "name": "unsign", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signed_value"], "arg_types": ["itsdangerous.signer.Signer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unsign of Signer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "validate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "signed_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer.validate", "name": "validate", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "signed_value"], "arg_types": ["itsdangerous.signer.Signer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate of Signer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.Signer.verify_signature", "name": "verify_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "value", "sig"], "arg_types": ["itsdangerous.signer.Signer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_signature of Signer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.signer.Signer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.signer.Signer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "SigningAlgorithm": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.signer.SigningAlgorithm", "name": "SigningAlgorithm", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.SigningAlgorithm", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.signer", "mro": ["itsdangerous.signer.SigningAlgorithm", "builtins.object"], "names": {".class": "SymbolTable", "get_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.SigningAlgorithm.get_signature", "name": "get_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "key", "value"], "arg_types": ["itsdangerous.signer.SigningAlgorithm", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_signature of SigningAlgorithm", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "verify_signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "value", "sig"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer.SigningAlgorithm.verify_signature", "name": "verify_signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "key", "value", "sig"], "arg_types": ["itsdangerous.signer.SigningAlgorithm", "builtins.bytes", "builtins.bytes", "builtins.bytes"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "verify_signature of SigningAlgorithm", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.signer.SigningAlgorithm.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.signer.SigningAlgorithm", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.signer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.signer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.signer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.signer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.signer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.signer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_base64_alphabet": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding._base64_alphabet", "kind": "Gdef"}, "_make_keys_list": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["secret_key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.signer._make_keys_list", "name": "_make_keys_list", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["secret_key"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_secret_key"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_keys_list", "ret_type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "_t_opt_str_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.signer._t_opt_str_bytes", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_t_secret_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.signer._t_secret_key", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.signer._t_str_bytes"}], "uses_pep604_syntax": false}}}, "_t_str_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.signer._t_str_bytes", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}}}, "base64_decode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_decode", "kind": "Gdef"}, "base64_encode": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.base64_encode", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "hmac": {".class": "SymbolTableNode", "cross_ref": "hmac", "kind": "Gdef"}, "want_bytes": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.want_bytes", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\itsdangerous\\signer.py"}
{".class": "MypyFile", "_fullname": "itsdangerous.serializer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BadPayload": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadPayload", "kind": "Gdef"}, "BadSignature": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.exc.BadSignature", "kind": "Gdef"}, "Serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "itsdangerous.serializer.Serializer", "name": "Serializer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "itsdangerous.serializer", "mro": ["itsdangerous.serializer.Serializer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_key", "salt", "serializer", "serializer_kwargs", "signer", "signer_kwargs", "fallback_signers"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "secret_key", "salt", "serializer", "serializer_kwargs", "signer", "signer_kwargs", "fallback_signers"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_secret_key"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_kwargs"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_signer"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_kwargs"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_fallbacks"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Serializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_loads_unsafe_impl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "s", "salt", "load_kwargs", "load_payload_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer._loads_unsafe_impl", "name": "_loads_unsafe_impl", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "s", "salt", "load_kwargs", "load_payload_kwargs"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_kwargs"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_kwargs"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_loads_unsafe_impl of Serializer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_load_unsafe"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "default_fallback_signers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "itsdangerous.serializer.Serializer.default_fallback_signers", "name": "default_fallback_signers", "type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_fallbacks"}}}, "default_serializer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "itsdangerous.serializer.Serializer.default_serializer", "name": "default_serializer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "default_signer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "itsdangerous.serializer.Serializer.default_signer", "name": "default_signer", "type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_signer"}}}, "dump": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "obj", "f", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.dump", "name": "dump", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "obj", "f", "salt"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump of Serializer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.dump_payload", "name": "dump_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "obj"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_payload of Serializer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dumps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.dumps", "name": "dumps", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "obj", "salt"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dumps of Serializer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_str_bytes"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fallback_signers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.fallback_signers", "name": "fallback_signers", "type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_fallbacks"}}}, "is_text_serializer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.is_text_serializer", "name": "is_text_serializer", "type": "builtins.bool"}}, "iter_unsigners": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.iter_unsigners", "name": "iter_unsigners", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "salt"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "iter_unsigners of Serializer", "ret_type": {".class": "Instance", "args": ["itsdangerous.signer.Signer"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "f", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.load", "name": "load", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "f", "salt"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load of Serializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_payload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "payload", "serializer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.load_payload", "name": "load_payload", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "payload", "serializer"], "arg_types": ["itsdangerous.serializer.Serializer", "builtins.bytes", {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_payload of Serializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_unsafe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "f", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.load_unsafe", "name": "load_unsafe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "f", "salt"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "typing.IO"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_unsafe of Serializer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_load_unsafe"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "s", "salt", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.loads", "name": "loads", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 4], "arg_names": ["self", "s", "salt", "kwargs"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loads of Serializer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loads_unsafe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.loads_unsafe", "name": "loads_unsafe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "s", "salt"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_str_bytes"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "loads_unsafe of Serializer", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_load_unsafe"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "make_signer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "salt"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.Serializer.make_signer", "name": "make_signer", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "salt"], "arg_types": ["itsdangerous.serializer.Serializer", {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_opt_str_bytes"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "make_signer of Serializer", "ret_type": "itsdangerous.signer.Signer", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "salt": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "itsdangerous.serializer.Serializer.salt", "name": "salt", "type": {".class": "UnionType", "items": ["builtins.bytes", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "secret_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "itsdangerous.serializer.Serializer.secret_key", "name": "secret_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["itsdangerous.serializer.Serializer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "secret_key of Serializer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.secret_key", "name": "secret_key", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["itsdangerous.serializer.Serializer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "secret_key of Serializer", "ret_type": "builtins.bytes", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "secret_keys": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.secret_keys", "name": "secret_keys", "type": {".class": "Instance", "args": ["builtins.bytes"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "serializer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.serializer", "name": "serializer", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "serializer_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.serializer_kwargs", "name": "serializer_kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_kwargs"}}}, "signer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.signer", "name": "signer", "type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_signer"}}}, "signer_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "itsdangerous.serializer.Serializer.signer_kwargs", "name": "signer_kwargs", "type": {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_kwargs"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "itsdangerous.serializer.Serializer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "itsdangerous.serializer.Serializer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Signer": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.signer.Signer", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.serializer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.serializer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.serializer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.serializer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.serializer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "itsdangerous.serializer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_make_keys_list": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.signer._make_keys_list", "kind": "Gdef"}, "_t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "_t_fallbacks": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_fallbacks", "line": 15, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_kwargs"}, {".class": "TupleType", "implicit": false, "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_signer"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_kwargs"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_signer"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_t_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_kwargs", "line": 12, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_t_load_unsafe": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_load_unsafe", "line": 16, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TupleType", "implicit": false, "items": ["builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_t_opt_kwargs": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_opt_kwargs", "line": 13, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_kwargs"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_t_opt_str_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_opt_str_bytes", "line": 11, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_str_bytes"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_t_secret_key": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_secret_key", "line": 17, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_str_bytes"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "TypeAliasType", "args": [], "type_ref": "itsdangerous.serializer._t_str_bytes"}], "uses_pep604_syntax": false}}}, "_t_signer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_signer", "line": 14, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "TypeType", "item": "itsdangerous.signer.Signer"}}}, "_t_str_bytes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "itsdangerous.serializer._t_str_bytes", "line": 10, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}}}, "is_text_serializer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["serializer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "itsdangerous.serializer.is_text_serializer", "name": "is_text_serializer", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["serializer"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_text_serializer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "want_bytes": {".class": "SymbolTableNode", "cross_ref": "itsdangerous.encoding.want_bytes", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\itsdangerous\\serializer.py"}
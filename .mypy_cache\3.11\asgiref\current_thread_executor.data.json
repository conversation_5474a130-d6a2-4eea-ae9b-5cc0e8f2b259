{".class": "MypyFile", "_fullname": "asgiref.current_thread_executor", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CurrentThreadExecutor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["concurrent.futures._base.Executor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor", "name": "CurrentThreadExecutor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asgiref.current_thread_executor", "mro": ["asgiref.current_thread_executor.CurrentThreadExecutor", "concurrent.futures._base.Executor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asgiref.current_thread_executor.CurrentThreadExecutor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CurrentThreadExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_broken": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor._broken", "name": "_broken", "type": "builtins.bool"}}, "_submit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "fn", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "name": "_submit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["self", "fn", "args", "kwargs"], "arg_types": ["asgiref.current_thread_executor.CurrentThreadExecutor", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "asgiref.current_thread_executor._P", "id": -1, "name": "_P", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "asgiref.current_thread_executor._P", "id": -1, "name": "_P", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -2, "name": "_R", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "asgiref.current_thread_executor._P", "id": -1, "name": "_P", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "asgiref.current_thread_executor._P", "id": -1, "name": "_P", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_submit of CurrentThreadExecutor", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -2, "name": "_R", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "asgiref.current_thread_executor._P", "id": -1, "name": "_P", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -2, "name": "_R", "namespace": "asgiref.current_thread_executor.CurrentThreadExecutor._submit", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_work_queue": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor._work_queue", "name": "_work_queue", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": ["asgiref.current_thread_executor._WorkItem", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "queue.Queue"}}}, "_work_thread": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor._work_thread", "name": "_work_thread", "type": "threading.Thread"}}, "run_until_future": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "future"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor.run_until_future", "name": "run_until_future", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "future"], "arg_types": ["asgiref.current_thread_executor.CurrentThreadExecutor", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run_until_future of CurrentThreadExecutor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor.CurrentThreadExecutor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asgiref.current_thread_executor.CurrentThreadExecutor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Executor": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Executor", "kind": "Gdef"}, "Future": {".class": "SymbolTableNode", "cross_ref": "concurrent.futures._base.Future", "kind": "Gdef"}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing.ParamSpec", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "_P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._P", "name": "_P", "upper_bound": "builtins.object", "variance": 0}}, "_R": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "name": "_R", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_T": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._T", "name": "_T", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "_WorkItem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "asgiref.current_thread_executor._WorkItem", "name": "_WorkItem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "asgiref.current_thread_executor._WorkItem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "asgiref.current_thread_executor", "mro": ["asgiref.current_thread_executor._WorkItem", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "future", "fn", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asgiref.current_thread_executor._WorkItem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 2, 4], "arg_names": ["self", "future", "fn", "args", "kwargs"], "arg_types": ["asgiref.current_thread_executor._WorkItem", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -1, "name": "_R", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -1, "name": "_R", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of _WorkItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -1, "name": "_R", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 0, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": "builtins.object"}]}}}, "args": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asgiref.current_thread_executor._WorkItem.args", "name": "args", "type": {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "fn": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asgiref.current_thread_executor._WorkItem.fn", "name": "fn", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 1, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -1, "name": "_R", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "upper_bound": "builtins.object", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "future": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asgiref.current_thread_executor._WorkItem.future", "name": "future", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._R", "id": -1, "name": "_R", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "concurrent.futures._base.Future"}}}, "kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "asgiref.current_thread_executor._WorkItem.kwargs", "name": "kwargs", "type": {".class": "ParamSpecType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "flavor": 2, "fullname": "asgiref.current_thread_executor._P", "id": -2, "name": "_P", "namespace": "asgiref.current_thread_executor._WorkItem.__init__", "prefix": {".class": "Parameters", "arg_kinds": [], "arg_names": [], "arg_types": [], "imprecise_arg_kinds": false, "variables": []}, "upper_bound": {".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}}}}, "run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "asgiref.current_thread_executor._WorkItem.run", "name": "run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["asgiref.current_thread_executor._WorkItem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "run of _WorkItem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "asgiref.current_thread_executor._WorkItem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "asgiref.current_thread_executor._WorkItem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asgiref.current_thread_executor.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asgiref.current_thread_executor.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asgiref.current_thread_executor.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asgiref.current_thread_executor.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asgiref.current_thread_executor.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "asgiref.current_thread_executor.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "queue": {".class": "SymbolTableNode", "cross_ref": "queue", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\asgiref\\current_thread_executor.py"}
{"data_mtime": 1751398266, "dep_lines": [3, 9, 11, 12, 14, 16, 17, 20, 23, 1, 3, 4, 5, 7, 8, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 25, 25, 5, 20, 10, 5, 5, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["importlib.metadata", "urllib.parse", "werkzeug.test", "click.testing", "werkzeug.wrappers", "flask.cli", "flask.sessions", "_typeshed.wsgi", "flask.app", "__future__", "importlib", "typing", "contextlib", "copy", "types", "werkzeug", "builtins", "_frozen_importlib", "_typeshed", "abc", "click", "flask.config", "flask.json", "flask.json.provider", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "json", "typing_extensions", "urllib", "werkzeug.datastructures", "werkzeug.datastructures.auth", "werkzeug.datastructures.headers", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.sansio.response", "werkzeug.wrappers.request", "werkzeug.wrappers.response", "wsgiref", "wsgiref.types"], "hash": "cb4c9304ab7463d4a164d527f017bf2f6f302745", "id": "flask.testing", "ignore_all": true, "interface_hash": "c81abb4d8fa57a69b39ce6b7e0a11b19b93bbf1e", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\testing.py", "plugin_data": null, "size": 10163, "suppressed": [], "version_id": "1.15.0"}
{"data_mtime": 1751401283, "dep_lines": [3, 2, 1, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 15, 16, 19, 20, 21, 543, 2572, 3631, 3972, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 10, 10, 10, 5, 10, 10, 5, 5, 5, 5, 5, 10, 10, 20, 10, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.middleware.cors", "fastapi.responses", "<PERSON><PERSON><PERSON>", "pydantic", "logging", "os", "sys", "json", "pathlib", "chromadb", "ollama", "typing", "datetime", "code_preprocessor", "vector_db_creator", "collections", "re", "asyncio", "<PERSON><PERSON><PERSON>", "traceback", "psutil", "u<PERSON><PERSON>", "builtins", "_frozen_importlib", "_typeshed", "abc", "annotated_types", "asyncio.protocols", "chromadb.api", "chromadb.config", "configparser", "contextlib", "enum", "fastapi.applications", "fastapi.openapi", "fastapi.openapi.models", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.networks", "pydantic.types", "starlette", "starlette.applications", "starlette.middleware", "starlette.middleware.cors", "starlette.requests", "starlette.responses", "starlette.routing", "types", "typing_extensions", "uvicorn._types"], "hash": "ac694e16c7b8de395078bbc9a0f5bad01d1803c4", "id": "main", "ignore_all": true, "interface_hash": "51f916d9c2e51d9a8af21dd31ce4860d702b002d", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\main.py", "plugin_data": null, "size": 187419, "suppressed": [], "version_id": "1.15.0"}
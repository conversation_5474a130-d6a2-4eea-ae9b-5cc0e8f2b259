{".class": "MypyFile", "_fullname": "jinja2.compiler", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CodeGenerator": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.visitor.NodeVisitor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.CodeGenerator", "name": "CodeGenerator", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.CodeGenerator", "jinja2.visitor.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "_FinalizeInfo": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo", "name": "_FinalizeInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_named_tuple"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"namedtuple": {"fields": ["const", "src"]}}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.CodeGenerator._FinalizeInfo", "builtins.tuple", "typing.Sequence", "typing.Reversible", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "_NT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "name": "_NT", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, "__annotations__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "const"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "src"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__new__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "const", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo.__new__", "name": "__new__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["_cls", "const", "src"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__new__ of _FinalizeInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo.__new__", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_asdict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["_self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._asdict", "name": "_asdict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["_self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_asdict of _FinalizeInfo", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._asdict", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_field_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._field_defaults", "name": "_field_defaults", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_field_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._field_types", "name": "_field_types", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_fields": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._fields", "name": "_fields", "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "_make": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _FinalizeInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_classmethod", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "name": "_make", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["_cls", "iterable"], "arg_types": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make of _FinalizeInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._make", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}}, "plugin_generated": true}, "_replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["_self", "const", "src"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._replace", "name": "_replace", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["_self", "const", "src"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_replace of _FinalizeInfo", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._NT", "id": -1, "name": "_NT", "namespace": "jinja2.compiler.CodeGenerator._FinalizeInfo._replace", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "values": [], "variance": 0}]}}, "plugin_generated": true}, "_source": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo._source", "name": "_source", "type": "builtins.str"}}, "const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo.const", "name": "const", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "const-redefinition": {".class": "SymbolTableNode", "cross_ref": "jinja2.compiler.CodeGenerator._FinalizeInfo.const", "kind": "<PERSON><PERSON><PERSON>"}, "src": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_property", "is_ready"], "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo.src", "name": "src", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "src-redefinition": {".class": "SymbolTableNode", "cross_ref": "jinja2.compiler.CodeGenerator._FinalizeInfo.src", "kind": "<PERSON><PERSON><PERSON>"}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator._FinalizeInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": "jinja2.compiler.CodeGenerator._FinalizeInfo"}, "values": [], "variance": 0}, "slots": null, "tuple_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}, "builtins.str"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "environment", "name", "filename", "stream", "defer_init", "optimized"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["self", "environment", "name", "filename", "stream", "defer_init", "optimized"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.environment.Environment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_assign_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._assign_stack", "name": "_assign_stack", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_context_reference_stack": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._context_reference_stack", "name": "_context_reference_stack", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_default_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "jinja2.compiler.CodeGenerator._default_finalize", "name": "_default_finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_finalize of CodeGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._default_finalize", "name": "_default_finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_default_finalize of CodeGenerator", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_filter_test_common": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "is_filter"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator._filter_test_common", "name": "_filter_test_common", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "is_filter"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "UnionType", "items": ["jinja2.nodes.Filter", "jinja2.nodes.Test"], "uses_pep604_syntax": false}, "jinja2.compiler.Frame", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_test_common of CodeGenerator", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._filter_test_common", "name": "_filter_test_common", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "is_filter"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "UnionType", "items": ["jinja2.nodes.Filter", "jinja2.nodes.Test"], "uses_pep604_syntax": false}, "jinja2.compiler.Frame", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_filter_test_common of CodeGenerator", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, {".class": "NoneType"}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "contextlib._GeneratorContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator._finalize", "name": "_finalize", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "jinja2.compiler.CodeGenerator._FinalizeInfo"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "_first_write": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._first_write", "name": "_first_write", "type": "builtins.bool"}}, "_import_common": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._import_common", "name": "_import_common", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "UnionType", "items": ["jinja2.nodes.Import", "jinja2.nodes.FromImport"], "uses_pep604_syntax": false}, "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_import_common of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_indentation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._indentation", "name": "_indentation", "type": "builtins.int"}}, "_last_identifier": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._last_identifier", "name": "_last_identifier", "type": "builtins.int"}}, "_last_line": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._last_line", "name": "_last_line", "type": "builtins.int"}}, "_make_finalize": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._make_finalize", "name": "_make_finalize", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_finalize of CodeGenerator", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "jinja2.compiler.CodeGenerator._FinalizeInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_new_lines": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._new_lines", "name": "_new_lines", "type": "builtins.int"}}, "_output_child_post": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "finalize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._output_child_post", "name": "_output_child_post", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "finalize"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Expr", "jinja2.compiler.Frame", {".class": "TypeAliasType", "args": [], "type_ref": "jinja2.compiler.CodeGenerator._FinalizeInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_output_child_post of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_output_child_pre": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "finalize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._output_child_pre", "name": "_output_child_pre", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "finalize"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Expr", "jinja2.compiler.Frame", {".class": "TypeAliasType", "args": [], "type_ref": "jinja2.compiler.CodeGenerator._FinalizeInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_output_child_pre of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_output_child_to_const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "finalize"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._output_child_to_const", "name": "_output_child_to_const", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "frame", "finalize"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Expr", "jinja2.compiler.Frame", {".class": "TypeAliasType", "args": [], "type_ref": "jinja2.compiler.CodeGenerator._FinalizeInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_output_child_to_const of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_output_const_repr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "group"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator._output_const_repr", "name": "_output_const_repr", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "group"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_output_const_repr of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_param_def_block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._param_def_block", "name": "_param_def_block", "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_write_debug_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator._write_debug_info", "name": "_write_debug_info", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "blocks": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.blocks", "name": "blocks", "type": {".class": "Instance", "args": ["builtins.str", "jinja2.nodes.Block"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "blockvisit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "nodes", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.blockvisit", "name": "blockvisit", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "nodes", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "blockvisit of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "buffer": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.buffer", "name": "buffer", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "buffer of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "choose_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "async_value", "sync_value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.choose_async", "name": "choose_async", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "async_value", "sync_value"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "choose_async of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "code_lineno": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.code_lineno", "name": "code_lineno", "type": "builtins.int"}}, "created_block_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.created_block_context", "name": "created_block_context", "type": "builtins.bool"}}, "debug_info": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.debug_info", "name": "debug_info", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "defer_init": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.defer_init", "name": "defer_init", "type": "builtins.bool"}}, "derive_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.derive_context", "name": "derive_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "derive_context of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "dump_local_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.dump_local_context", "name": "dump_local_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "dump_local_context of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "end_write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.end_write", "name": "end_write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "end_write of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enter_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.enter_frame", "name": "enter_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "enter_frame of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "environment": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.environment", "name": "environment", "type": "jinja2.environment.Environment"}}, "extends_so_far": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.extends_so_far", "name": "extends_so_far", "type": "builtins.int"}}, "fail": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "lineno"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.fail", "name": "fail", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "msg", "lineno"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fail of CodeGenerator", "ret_type": {".class": "UninhabitedType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filename": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.filename", "name": "filename", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "filters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.filters", "name": "filters", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.func", "name": "func", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "name"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "func of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_context_ref": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.get_context_ref", "name": "get_context_ref", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_context_ref of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_resolve_func": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.get_resolve_func", "name": "get_resolve_func", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_resolve_func of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_known_extends": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.has_known_extends", "name": "has_known_extends", "type": "builtins.bool"}}, "import_aliases": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.import_aliases", "name": "import_aliases", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "indent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.indent", "name": "indent", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "indent of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "leave_frame": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "frame", "with_python_scope"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.leave_frame", "name": "leave_frame", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "frame", "with_python_scope"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "leave_frame of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "macro_body": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.macro_body", "name": "macro_body", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "UnionType", "items": ["jinja2.nodes.Macro", "jinja2.nodes.CallBlock"], "uses_pep604_syntax": false}, "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "macro_body of CodeGenerator", "ret_type": {".class": "TupleType", "implicit": false, "items": ["jinja2.compiler.Frame", "jinja2.compiler.MacroRef"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "macro_def": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "macro_ref", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.macro_def", "name": "macro_def", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "macro_ref", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.MacroRef", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "macro_def of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "mark_parameter_stored": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.mark_parameter_stored", "name": "mark_parameter_stored", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "mark_parameter_stored of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.name", "name": "name", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "newline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "node", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.newline", "name": "newline", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "node", "extra"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "UnionType", "items": ["jinja2.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "newline of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "optimized": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.optimized", "name": "optimized", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optimized of CodeGenerator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.optimized", "name": "optimized", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optimized of CodeGenerator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "optimizer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.optimizer", "name": "optimizer", "type": {".class": "UnionType", "items": ["jinja2.optimizer.Optimizer", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "outdent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "step"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.outdent", "name": "outdent", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "step"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "outdent of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parameter_is_undeclared": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.parameter_is_undeclared", "name": "parameter_is_undeclared", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "parameter_is_undeclared of CodeGenerator", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_assign_tracking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.pop_assign_tracking", "name": "pop_assign_tracking", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_assign_tracking of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_context_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.pop_context_reference", "name": "pop_context_reference", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_context_reference of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pop_parameter_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.pop_parameter_definitions", "name": "pop_parameter_definitions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pop_parameter_definitions of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "position": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.position", "name": "position", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Node"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "position of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pull_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.pull_dependencies", "name": "pull_dependencies", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "nodes"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pull_dependencies of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_assign_tracking": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.push_assign_tracking", "name": "push_assign_tracking", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_assign_tracking of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_context_reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.push_context_reference", "name": "push_context_reference", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_context_reference of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "push_parameter_definitions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.push_parameter_definitions", "name": "push_parameter_definitions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "push_parameter_definitions of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "return_buffer_contents": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "frame", "force_unescaped"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.return_buffer_contents", "name": "return_buffer_contents", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "frame", "force_unescaped"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "return_buffer_contents of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "signature": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "frame", "extra_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.signature", "name": "signature", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "frame", "extra_kwargs"], "arg_types": ["jinja2.compiler.CodeGenerator", {".class": "UnionType", "items": ["jinja2.nodes.Call", "jinja2.nodes.Filter", "jinja2.nodes.Test"], "uses_pep604_syntax": false}, "jinja2.compiler.Frame", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "signature of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "simple_write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "s", "frame", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.simple_write", "name": "simple_write", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "s", "frame", "node"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str", "jinja2.compiler.Frame", {".class": "UnionType", "items": ["jinja2.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "simple_write of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "start_write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "frame", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.start_write", "name": "start_write", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "frame", "node"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.compiler.Frame", {".class": "UnionType", "items": ["jinja2.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "start_write of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.stream", "name": "stream", "type": "typing.TextIO"}}, "temporary_identifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.temporary_identifier", "name": "temporary_identifier", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "temporary_identifier of CodeGenerator", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "tests": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.tests", "name": "tests", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "visit_Add": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Add", "name": "visit_Add", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_And": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_And", "name": "visit_And", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Assign": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Assign", "name": "visit_Assign", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Assign", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Assign of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_AssignBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_AssignBlock", "name": "visit_AssignBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.AssignBlock", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_AssignBlock of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Block", "name": "visit_Block", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Block", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Block of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Break": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Break", "name": "visit_Break", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Break", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Break of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "frame", "forward_caller"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_Call", "name": "visit_Call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "frame", "forward_caller"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Call", "jinja2.compiler.Frame", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Call of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_Call", "name": "visit_Call", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "node", "frame", "forward_caller"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Call", "jinja2.compiler.Frame", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Call of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_CallBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_CallBlock", "name": "visit_CallBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.CallBlock", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_CallBlock of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Compare": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_Compare", "name": "visit_Compare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Compare", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Compare of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_Compare", "name": "visit_Compare", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Compare", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Compare of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Concat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_Concat", "name": "visit_Concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Concat", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Concat of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_Concat", "name": "visit_Concat", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Concat", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Concat of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_CondExpr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_CondExpr", "name": "visit_CondExpr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.CondExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_CondExpr of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_CondExpr", "name": "visit_CondExpr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.CondExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_CondExpr of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Const": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Const", "name": "visit_Const", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Const", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Const of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ContextReference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_ContextReference", "name": "visit_ContextReference", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.ContextReference", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ContextReference of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Continue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Continue", "name": "visit_Continue", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Continue", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Continue of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_DerivedContextReference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_DerivedContextReference", "name": "visit_DerivedContextReference", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.DerivedContextReference", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_DerivedContextReference of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Dict", "name": "visit_Dict", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Dict", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Dict of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Div": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Div", "name": "visit_Div", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_EnvironmentAttribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_EnvironmentAttribute", "name": "visit_EnvironmentAttribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.EnvironmentAttribute", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_EnvironmentAttribute of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_EvalContextModifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_EvalContextModifier", "name": "visit_EvalContextModifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.EvalContextModifier", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_EvalContextModifier of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ExprStmt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_ExprStmt", "name": "visit_ExprStmt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.ExprStmt", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ExprStmt of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Extends": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Extends", "name": "visit_Extends", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Extends", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Extends of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ExtensionAttribute": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_ExtensionAttribute", "name": "visit_ExtensionAttribute", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.ExtensionAttribute", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ExtensionAttribute of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_Filter", "name": "visit_Filter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Filter", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Filter of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_Filter", "name": "visit_Filter", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Filter", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Filter of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_FilterBlock": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_FilterBlock", "name": "visit_FilterBlock", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.FilterBlock", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_FilterBlock of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FloorDiv": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_FloorDiv", "name": "visit_FloorDiv", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_For": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_For", "name": "visit_For", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.For", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_For of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_FromImport": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_FromImport", "name": "visit_FromImport", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.FromImport", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_FromImport of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Getattr": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_Getattr", "name": "visit_Getattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Getattr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Getattr of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_Getattr", "name": "visit_Getattr", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Getattr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Getattr of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Getitem": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_Getitem", "name": "visit_Getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Getitem", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Getitem of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_Getitem", "name": "visit_Getitem", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Getitem", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Getitem of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_If": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_If", "name": "visit_If", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.If", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_If of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Import": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Import", "name": "visit_Import", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Import", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Import of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ImportedName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_ImportedName", "name": "visit_ImportedName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.ImportedName", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ImportedName of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Include": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Include", "name": "visit_Include", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Include", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Include of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_InternalName": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_InternalName", "name": "visit_InternalName", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.InternalName", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_InternalName of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Keyword": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Keyword", "name": "visit_Keyword", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Keyword", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Keyword of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_List": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_List", "name": "visit_List", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.List", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_List of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Macro": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Macro", "name": "visit_<PERSON>ro", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Macro", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Macro of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_MarkSafe": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_MarkSafe", "name": "visit_MarkSafe", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.MarkSafe", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_MarkSafe of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_MarkSafeIfAutoescape": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_MarkSafeIfAutoescape", "name": "visit_MarkSafeIfAutoescape", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.MarkSafeIfAutoescape", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_MarkSafeIfAutoescape of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Mod": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Mod", "name": "visit_Mod", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Mul": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Mul", "name": "visit_Mul", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_NSRef": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_NSRef", "name": "visit_NSRef", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.NSRef", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_NSRef of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Name", "name": "visit_Name", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Name", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Name of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Neg": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Neg", "name": "visit_Neg", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.UnaryExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Not": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Not", "name": "visit_Not", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.UnaryExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Operand": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Operand", "name": "visit_Operand", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Operand", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Operand of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Or": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Or", "name": "visit_Or", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Output", "name": "visit_Output", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Output", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Output of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_OverlayScope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_OverlayScope", "name": "visit_OverlayScope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.OverlayScope", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_OverlayScope of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Pos": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Pos", "name": "visit_Pos", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.UnaryExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Pow": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Pow", "name": "visit_Pow", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Scope": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Scope", "name": "visit_Scope", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Scope", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Scope of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_ScopedEvalContextModifier": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_ScopedEvalContextModifier", "name": "visit_ScopedEvalContextModifier", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.ScopedEvalContextModifier", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_ScopedEvalContextModifier of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Slice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Slice", "name": "visit_Slice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Slice", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Slice of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Sub": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.CodeGenerator.visit_Sub", "name": "visit_Sub", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Template": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Template", "name": "visit_Template", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Template", {".class": "UnionType", "items": ["jinja2.compiler.Frame", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Template of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_TemplateData": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_TemplateData", "name": "visit_TemplateData", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.TemplateData", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_TemplateData of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "jinja2.compiler.CodeGenerator.visit_Test", "name": "visit_Test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Test", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Test of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.CodeGenerator.visit_Test", "name": "visit_Test", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Test", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Test of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "visit_Tuple": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_Tuple", "name": "visit_<PERSON><PERSON>", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.Tuple", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Tuple of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_With": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.visit_With", "name": "visit_With", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "node", "frame"], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.With", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_With of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.write", "name": "write", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "x"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "write_commons": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.write_commons", "name": "write_commons", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.CodeGenerator"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "write_commons of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "writeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "x", "node", "extra"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CodeGenerator.writeline", "name": "writeline", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "x", "node", "extra"], "arg_types": ["jinja2.compiler.CodeGenerator", "builtins.str", {".class": "UnionType", "items": ["jinja2.nodes.Node", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "writeline of CodeGenerator", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CodeGenerator.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.compiler.CodeGenerator", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CompilerExit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.CompilerExit", "name": "CompilerExit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.compiler.CompilerExit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.CompilerExit", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.CompilerExit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.compiler.CompilerExit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "DependencyFinderVisitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.visitor.NodeVisitor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.DependencyFinderVisitor", "name": "DependencyFinderVisitor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.compiler.DependencyFinderVisitor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.DependencyFinderVisitor", "jinja2.visitor.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.DependencyFinderVisitor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.DependencyFinderVisitor"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DependencyFinderVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "filters": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.DependencyFinderVisitor.filters", "name": "filters", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "tests": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.DependencyFinderVisitor.tests", "name": "tests", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "visit_Block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.DependencyFinderVisitor.visit_Block", "name": "visit_Block", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.compiler.DependencyFinderVisitor", "jinja2.nodes.Block"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Block of DependencyFinderVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Filter": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.DependencyFinderVisitor.visit_Filter", "name": "visit_Filter", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.compiler.DependencyFinderVisitor", "jinja2.nodes.Filter"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Filter of DependencyFinderVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Test": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.DependencyFinderVisitor.visit_Test", "name": "visit_Test", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.compiler.DependencyFinderVisitor", "jinja2.nodes.Test"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Test of DependencyFinderVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.DependencyFinderVisitor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.compiler.DependencyFinderVisitor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Environment": {".class": "SymbolTableNode", "cross_ref": "jinja2.environment.Environment", "kind": "Gdef"}, "EvalContext": {".class": "SymbolTableNode", "cross_ref": "jinja2.nodes.EvalContext", "kind": "Gdef"}, "F": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.F", "name": "F", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}}, "Frame": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.Frame", "name": "<PERSON>ame", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.compiler.Frame", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.Frame", "builtins.object"], "names": {".class": "SymbolTable", "__copy__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.Frame.__copy__", "name": "__copy__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "jinja2.compiler.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "eval_ctx", "parent", "level"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.Frame.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "eval_ctx", "parent", "level"], "arg_types": ["jinja2.compiler.Frame", "jinja2.nodes.EvalContext", {".class": "UnionType", "items": ["jinja2.compiler.Frame", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Frame", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "block": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.Frame.block", "name": "block", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "block_frame": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.block_frame", "name": "block_frame", "type": "builtins.bool"}}, "buffer": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.Frame.buffer", "name": "buffer", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.Frame.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "copy of <PERSON><PERSON>", "ret_type": "jinja2.compiler.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "eval_ctx": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.eval_ctx", "name": "eval_ctx", "type": "jinja2.nodes.EvalContext"}}, "inner": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "isolated"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.Frame.inner", "name": "inner", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "isolated"], "arg_types": ["jinja2.compiler.Frame", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inner of Frame", "ret_type": "jinja2.compiler.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "loop_frame": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.loop_frame", "name": "loop_frame", "type": "builtins.bool"}}, "parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.parent", "name": "parent", "type": {".class": "UnionType", "items": ["jinja2.compiler.Frame", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "require_output_check": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.require_output_check", "name": "require_output_check", "type": "builtins.bool"}}, "rootlevel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.rootlevel", "name": "rootlevel", "type": "builtins.bool"}}, "soft": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.Frame.soft", "name": "soft", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["jinja2.compiler.Frame"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "soft of Frame", "ret_type": "jinja2.compiler.Frame", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "soft_frame": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.soft_frame", "name": "soft_frame", "type": "builtins.bool"}}, "symbols": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.symbols", "name": "symbols", "type": "jinja2.idtracking.Symbols"}}, "toplevel": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.Frame.toplevel", "name": "toplevel", "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.Frame.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.compiler.Frame", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MacroRef": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.MacroRef", "name": "MacroRef", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.compiler.MacroRef", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.MacroRef", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.MacroRef.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.compiler.MacroRef", {".class": "UnionType", "items": ["jinja2.nodes.Macro", "jinja2.nodes.CallBlock"], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MacroRef", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accesses_caller": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.MacroRef.accesses_caller", "name": "accesses_caller", "type": "builtins.bool"}}, "accesses_kwargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.MacroRef.accesses_kwargs", "name": "accesses_kwargs", "type": "builtins.bool"}}, "accesses_varargs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.MacroRef.accesses_varargs", "name": "accesses_varargs", "type": "builtins.bool"}}, "node": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.MacroRef.node", "name": "node", "type": {".class": "UnionType", "items": ["jinja2.nodes.Macro", "jinja2.nodes.CallBlock"], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.MacroRef.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.compiler.MacroRef", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Markup": {".class": "SymbolTableNode", "cross_ref": "markupsafe.Markup", "kind": "Gdef"}, "NodeVisitor": {".class": "SymbolTableNode", "cross_ref": "jinja2.visitor.NodeVisitor", "kind": "Gdef"}, "Optimizer": {".class": "SymbolTableNode", "cross_ref": "jinja2.optimizer.Optimizer", "kind": "Gdef"}, "StringIO": {".class": "SymbolTableNode", "cross_ref": "_io.StringIO", "kind": "Gdef"}, "Symbols": {".class": "SymbolTableNode", "cross_ref": "jinja2.idtracking.Symbols", "kind": "Gdef"}, "TemplateAssertionError": {".class": "SymbolTableNode", "cross_ref": "jinja2.exceptions.TemplateAssertionError", "kind": "Gdef"}, "UndeclaredNameVisitor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["jinja2.visitor.NodeVisitor"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.UndeclaredNameVisitor", "name": "UndeclaredNameVisitor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.compiler.UndeclaredNameVisitor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.UndeclaredNameVisitor", "jinja2.visitor.NodeVisitor", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.UndeclaredNameVisitor.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "names"], "arg_types": ["jinja2.compiler.UndeclaredNameVisitor", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of UndeclaredNameVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "names": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "jinja2.compiler.UndeclaredNameVisitor.names", "name": "names", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "undeclared": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "jinja2.compiler.UndeclaredNameVisitor.undeclared", "name": "undeclared", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "visit_Block": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.UndeclaredNameVisitor.visit_Block", "name": "visit_Block", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.compiler.UndeclaredNameVisitor", "jinja2.nodes.Block"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Block of UndeclaredNameVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "visit_Name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "node"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.UndeclaredNameVisitor.visit_Name", "name": "visit_Name", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "node"], "arg_types": ["jinja2.compiler.UndeclaredNameVisitor", "jinja2.nodes.Name"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "visit_Name of UndeclaredNameVisitor", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.UndeclaredNameVisitor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.compiler.UndeclaredNameVisitor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "VAR_LOAD_ALIAS": {".class": "SymbolTableNode", "cross_ref": "jinja2.idtracking.VAR_LOAD_ALIAS", "kind": "Gdef"}, "VAR_LOAD_PARAMETER": {".class": "SymbolTableNode", "cross_ref": "jinja2.idtracking.VAR_LOAD_PARAMETER", "kind": "Gdef"}, "VAR_LOAD_RESOLVE": {".class": "SymbolTableNode", "cross_ref": "jinja2.idtracking.VAR_LOAD_RESOLVE", "kind": "Gdef"}, "VAR_LOAD_UNDEFINED": {".class": "SymbolTableNode", "cross_ref": "jinja2.idtracking.VAR_LOAD_UNDEFINED", "kind": "Gdef"}, "VisitorExit": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.RuntimeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "jinja2.compiler.VisitorExit", "name": "VisitorExit", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "jinja2.compiler.VisitorExit", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "jinja2.compiler", "mro": ["jinja2.compiler.VisitorExit", "builtins.RuntimeError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.VisitorExit.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "jinja2.compiler.VisitorExit", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_PassArg": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils._PassArg", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.compiler.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.compiler.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.compiler.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.compiler.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.compiler.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "jinja2.compiler.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_make_binop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler._make_binop", "name": "_make_binop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_binop", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.BinExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_unop": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["op"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler._make_unop", "name": "_make_unop", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["op"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_make_unop", "ret_type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": [null, null, null], "arg_types": ["jinja2.compiler.CodeGenerator", "jinja2.nodes.UnaryExpr", "jinja2.compiler.Frame"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chain": {".class": "SymbolTableNode", "cross_ref": "itertools.chain", "kind": "Gdef"}, "concat": {".class": "SymbolTableNode", "cross_ref": "jinja2.utils.concat", "kind": "Gdef"}, "contextmanager": {".class": "SymbolTableNode", "cross_ref": "contextlib.contextmanager", "kind": "Gdef"}, "escape": {".class": "SymbolTableNode", "cross_ref": "markupsafe._speedups.escape", "kind": "Gdef"}, "find_undeclared": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["nodes", "names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.find_undeclared", "name": "find_undeclared", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["nodes", "names"], "arg_types": [{".class": "Instance", "args": ["jinja2.nodes.Node"], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "find_undeclared", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["node", "environment", "name", "filename", "stream", "defer_init", "optimized"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.generate", "name": "generate", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1, 1, 1], "arg_names": ["node", "environment", "name", "filename", "stream", "defer_init", "optimized"], "arg_types": ["jinja2.nodes.Template", "jinja2.environment.Environment", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["typing.TextIO", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_safe_repr": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.has_safe_repr", "name": "has_safe_repr", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_safe_repr", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_python_keyword": {".class": "SymbolTableNode", "cross_ref": "keyword.iskeyword", "kind": "Gdef"}, "nodes": {".class": "SymbolTableNode", "cross_ref": "jinja2.nodes", "kind": "Gdef"}, "operators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "jinja2.compiler.operators", "name": "operators", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "optimizeconst": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["f"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "jinja2.compiler.optimizeconst", "name": "optimizeconst", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["f"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.F", "id": -1, "name": "F", "namespace": "jinja2.compiler.optimizeconst", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "optimizeconst", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.F", "id": -1, "name": "F", "namespace": "jinja2.compiler.optimizeconst", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "jinja2.compiler.F", "id": -1, "name": "F", "namespace": "jinja2.compiler.optimizeconst", "upper_bound": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "values": [], "variance": 0}]}}}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "te": {".class": "SymbolTableNode", "cross_ref": "typing_extensions", "kind": "Gdef"}, "update_wrapper": {".class": "SymbolTableNode", "cross_ref": "functools.update_wrapper", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\jinja2\\compiler.py"}
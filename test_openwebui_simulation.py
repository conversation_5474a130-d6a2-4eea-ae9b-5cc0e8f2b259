#!/usr/bin/env python3
"""
Simulate exactly what OpenWebUI is doing to debug the 1 chunk issue.
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path to import the tool
sys.path.append(str(Path(__file__).parent))

from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter to capture status messages"""
    def __init__(self):
        self.events = []
    
    async def __call__(self, event):
        self.events.append(event)
        print(f"📡 Event: {event}")

async def test_openwebui_simulation():
    """Simulate exactly what OpenWebUI does"""
    print("🔍 Simulating OpenWebUI Behavior")
    print("=" * 60)
    
    # Initialize the tool exactly like OpenWebUI would
    tool = Tools()
    tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
    
    # Create mock event emitter
    event_emitter = MockEventEmitter()
    
    print("1. Clearing cache to ensure fresh results...")
    if tool.cache:
        await tool.clear_cache()
        print("   ✅ Cache cleared")
    
    print("\n2. Simulating 'select the utils codebase' command...")
    select_result = await tool.__call__("select the utils codebase")
    print(f"   Selection result: {select_result[:200]}...")
    print(f"   Current codebase after selection: {tool.valves.current_codebase}")
    
    print("\n3. Simulating 'how is memory managed in the utils codebase?' query...")
    
    # Test the main entry point exactly like OpenWebUI would call it
    try:
        result = await tool.__call__("how is memory managed in the utils codebase?")
        print(f"   Result length: {len(result)} characters")
        
        # Count chunks manually
        import re
        context_matches = re.findall(r"Context \d+:", result)
        code_blocks = result.count("```") // 2
        
        print(f"   Manual chunk count (Context X:): {len(context_matches)}")
        print(f"   Code blocks: {code_blocks}")
        
        if len(context_matches) > 1:
            print(f"   ✅ SUCCESS: Found {len(context_matches)} chunks via main entry!")
        else:
            print(f"   ⚠️ Only {len(context_matches)} chunk via main entry")
            print(f"   First 300 chars: {result[:300]}...")
            
            # Try direct function call as backup
            print("\n4. Testing direct get_code_context call...")
            direct_result = await tool.get_code_context(
                "how is memory managed in the utils codebase?",
                codebase_name="utils",
                n_results=15,
                __event_emitter__=event_emitter
            )
            
            direct_context_matches = re.findall(r"Context \d+:", direct_result)
            print(f"   Direct call chunk count: {len(direct_context_matches)}")
            print(f"   Direct call result length: {len(direct_result)}")
            
            if len(direct_context_matches) > len(context_matches):
                print(f"   🔍 ISSUE FOUND: Direct call gets {len(direct_context_matches)} chunks, main entry gets {len(context_matches)}")
                print("   This suggests a routing issue in the main __call__ method")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n5. Testing with alternative memory queries...")
    alt_queries = [
        "memory allocation in utils",
        "tmwmem functions",
        "show me memory management code"
    ]
    
    for query in alt_queries:
        print(f"\n   Testing: '{query}'")
        try:
            result = await tool.__call__(query)
            context_matches = re.findall(r"Context \d+:", result)
            print(f"     Chunks found: {len(context_matches)}")
            if len(context_matches) > 1:
                print(f"     ✅ This query works better!")
                break
        except Exception as e:
            print(f"     ❌ Error: {e}")
    
    print("\n" + "=" * 60)
    print("🎉 OpenWebUI simulation completed!")

if __name__ == "__main__":
    try:
        asyncio.run(test_openwebui_simulation())
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        sys.exit(1)

{"data_mtime": 1751402271, "dep_lines": [9, 6, 7, 8, 14, 223, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["unittest.mock", "asyncio", "sys", "pathlib", "open_webui_code_analyzer_tool", "traceback", "builtins", "_frozen_importlib", "_typeshed", "abc", "os", "typing", "typing_extensions", "unittest"], "hash": "5965d2fbc349c5689afb6c807f7c15e8d0e9335f", "id": "test_simplified_openwebui_tool", "ignore_all": false, "interface_hash": "8a025f498dc8c7a494db6f10e5981f80c1822c26", "mtime": 1751402269, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_simplified_openwebui_tool.py", "plugin_data": null, "size": 8770, "suppressed": [], "version_id": "1.15.0"}
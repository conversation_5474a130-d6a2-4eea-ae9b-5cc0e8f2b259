#!/usr/bin/env python3
"""
Phase 1: Vector Database Interface for Dynamic Codebase Analysis
Provides unified interface for different vector databases to support chunk retrieval.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Optional, Any
import json
import logging

class VectorDBInterface(ABC):
    """Abstract interface for vector database operations"""
    
    @abstractmethod
    def get_all_chunks_for_codebase(self, codebase_name: str, offset: int = 0, limit: int = 1000) -> List[Dict]:
        """Get all chunks for a codebase with pagination"""
        pass
    
    @abstractmethod
    def count_chunks_for_codebase(self, codebase_name: str) -> int:
        """Count total chunks for a codebase"""
        pass
    
    @abstractmethod
    def get_codebase_metadata(self, codebase_name: str) -> Dict:
        """Get metadata about a codebase"""
        pass

class ChromaDBInterface(VectorDBInterface):
    """ChromaDB implementation for chunk retrieval"""
    
    def __init__(self, client, logger=None):
        self.client = client
        self.logger = logger or logging.getLogger(__name__)
    
    def get_all_chunks_for_codebase(self, codebase_name: str, offset: int = 0, limit: int = 1000) -> List[Dict]:
        """Get all chunks for a codebase from ChromaDB"""
        try:
            # Get the collection for this codebase
            collection = self.client.get_collection(name=codebase_name)
            
            # Get chunks with pagination
            results = collection.get(
                offset=offset,
                limit=limit,
                include=['documents', 'metadatas', 'ids']
            )
            
            chunks = []
            documents = results.get('documents', [])
            metadatas = results.get('metadatas', [])
            ids = results.get('ids', [])
            
            for i, (doc, metadata, chunk_id) in enumerate(zip(documents, metadatas, ids)):
                chunks.append({
                    'content': doc,
                    'metadata': {
                        'chunk_id': chunk_id,
                        'file_path': metadata.get('file_path', ''),
                        'language': metadata.get('language', ''),
                        'semantic_tags': metadata.get('semantic_tags', []),
                        'function_names': metadata.get('function_names', []),
                        'complexity': metadata.get('complexity', 'unknown'),
                        'quality': metadata.get('quality', 'unknown'),
                        'line_start': metadata.get('line_start', 0),
                        'line_end': metadata.get('line_end', 0),
                        'chunk_type': metadata.get('chunk_type', 'code')
                    }
                })
            
            self.logger.info(f"Retrieved {len(chunks)} chunks for {codebase_name} (offset: {offset})")
            return chunks
            
        except Exception as e:
            self.logger.error(f"Failed to get chunks for {codebase_name}: {e}")
            return []
    
    def count_chunks_for_codebase(self, codebase_name: str) -> int:
        """Count total chunks for a codebase in ChromaDB"""
        try:
            collection = self.client.get_collection(name=codebase_name)
            return collection.count()
        except Exception as e:
            self.logger.error(f"Failed to count chunks for {codebase_name}: {e}")
            return 0
    
    def get_codebase_metadata(self, codebase_name: str) -> Dict:
        """Get metadata about a codebase from ChromaDB"""
        try:
            collection = self.client.get_collection(name=codebase_name)
            
            # Get sample of chunks to analyze metadata
            sample_results = collection.get(limit=100, include=['metadatas'])
            metadatas = sample_results.get('metadatas', [])
            
            # Analyze metadata to build codebase info
            languages = set()
            file_types = set()
            complexities = set()
            qualities = set()
            
            for metadata in metadatas:
                if metadata.get('language'):
                    languages.add(metadata['language'])
                if metadata.get('file_path'):
                    file_ext = metadata['file_path'].split('.')[-1] if '.' in metadata['file_path'] else ''
                    if file_ext:
                        file_types.add(file_ext)
                if metadata.get('complexity'):
                    complexities.add(metadata['complexity'])
                if metadata.get('quality'):
                    qualities.add(metadata['quality'])
            
            return {
                'name': codebase_name,
                'total_chunks': self.count_chunks_for_codebase(codebase_name),
                'languages': list(languages),
                'file_types': list(file_types),
                'complexities': list(complexities),
                'qualities': list(qualities),
                'status': 'ready'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get metadata for {codebase_name}: {e}")
            return {'name': codebase_name, 'status': 'error', 'error': str(e)}

class PineconeInterface(VectorDBInterface):
    """Pinecone implementation for chunk retrieval"""
    
    def __init__(self, client, index_name: str, logger=None):
        self.client = client
        self.index_name = index_name
        self.logger = logger or logging.getLogger(__name__)
    
    def get_all_chunks_for_codebase(self, codebase_name: str, offset: int = 0, limit: int = 1000) -> List[Dict]:
        """Get all chunks for a codebase from Pinecone"""
        try:
            index = self.client.Index(self.index_name)
            
            # Pinecone doesn't have direct "get all" - need to use query with filter
            # Create a dummy vector for querying
            dummy_vector = [0.0] * 1536  # Assuming 1536-dimensional embeddings
            
            results = index.query(
                vector=dummy_vector,
                filter={"codebase": codebase_name},
                top_k=limit,
                include_metadata=True
            )
            
            chunks = []
            for match in results.get('matches', []):
                metadata = match.get('metadata', {})
                chunks.append({
                    'content': metadata.get('content', ''),
                    'metadata': {
                        'chunk_id': match.get('id', ''),
                        'file_path': metadata.get('file_path', ''),
                        'language': metadata.get('language', ''),
                        'semantic_tags': metadata.get('semantic_tags', []),
                        'function_names': metadata.get('function_names', []),
                        'complexity': metadata.get('complexity', 'unknown'),
                        'quality': metadata.get('quality', 'unknown'),
                        'score': match.get('score', 0.0)
                    }
                })
            
            self.logger.info(f"Retrieved {len(chunks)} chunks for {codebase_name} from Pinecone")
            return chunks
            
        except Exception as e:
            self.logger.error(f"Failed to get chunks for {codebase_name} from Pinecone: {e}")
            return []
    
    def count_chunks_for_codebase(self, codebase_name: str) -> int:
        """Count total chunks for a codebase in Pinecone"""
        try:
            index = self.client.Index(self.index_name)
            
            # Pinecone doesn't have direct count with filter
            # Use describe_index_stats with filter
            stats = index.describe_index_stats(filter={"codebase": codebase_name})
            return stats.get('total_vector_count', 0)
            
        except Exception as e:
            self.logger.error(f"Failed to count chunks for {codebase_name} in Pinecone: {e}")
            return 0
    
    def get_codebase_metadata(self, codebase_name: str) -> Dict:
        """Get metadata about a codebase from Pinecone"""
        try:
            # Get sample chunks to analyze
            chunks = self.get_all_chunks_for_codebase(codebase_name, limit=100)
            
            languages = set()
            file_types = set()
            
            for chunk in chunks:
                metadata = chunk.get('metadata', {})
                if metadata.get('language'):
                    languages.add(metadata['language'])
                if metadata.get('file_path'):
                    file_ext = metadata['file_path'].split('.')[-1] if '.' in metadata['file_path'] else ''
                    if file_ext:
                        file_types.add(file_ext)
            
            return {
                'name': codebase_name,
                'total_chunks': self.count_chunks_for_codebase(codebase_name),
                'languages': list(languages),
                'file_types': list(file_types),
                'status': 'ready'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get metadata for {codebase_name} from Pinecone: {e}")
            return {'name': codebase_name, 'status': 'error', 'error': str(e)}

class WeaviateInterface(VectorDBInterface):
    """Weaviate implementation for chunk retrieval"""
    
    def __init__(self, client, class_name: str, logger=None):
        self.client = client
        self.class_name = class_name
        self.logger = logger or logging.getLogger(__name__)
    
    def get_all_chunks_for_codebase(self, codebase_name: str, offset: int = 0, limit: int = 1000) -> List[Dict]:
        """Get all chunks for a codebase from Weaviate"""
        try:
            query = (
                self.client.query
                .get(self.class_name, ['content', 'file_path', 'language', 'semantic_tags', 
                                      'function_names', 'complexity', 'quality'])
                .with_where({
                    'path': ['codebase'],
                    'operator': 'Equal',
                    'valueText': codebase_name
                })
                .with_limit(limit)
                .with_offset(offset)
            )
            
            results = query.do()
            
            chunks = []
            data = results.get('data', {}).get('Get', {}).get(self.class_name, [])
            
            for item in data:
                chunks.append({
                    'content': item.get('content', ''),
                    'metadata': {
                        'chunk_id': item.get('_additional', {}).get('id', ''),
                        'file_path': item.get('file_path', ''),
                        'language': item.get('language', ''),
                        'semantic_tags': item.get('semantic_tags', []),
                        'function_names': item.get('function_names', []),
                        'complexity': item.get('complexity', 'unknown'),
                        'quality': item.get('quality', 'unknown')
                    }
                })
            
            self.logger.info(f"Retrieved {len(chunks)} chunks for {codebase_name} from Weaviate")
            return chunks
            
        except Exception as e:
            self.logger.error(f"Failed to get chunks for {codebase_name} from Weaviate: {e}")
            return []
    
    def count_chunks_for_codebase(self, codebase_name: str) -> int:
        """Count total chunks for a codebase in Weaviate"""
        try:
            query = (
                self.client.query
                .aggregate(self.class_name)
                .with_where({
                    'path': ['codebase'],
                    'operator': 'Equal',
                    'valueText': codebase_name
                })
                .with_meta_count()
            )
            
            results = query.do()
            count_data = results.get('data', {}).get('Aggregate', {}).get(self.class_name, [])
            
            if count_data:
                return count_data[0].get('meta', {}).get('count', 0)
            return 0
            
        except Exception as e:
            self.logger.error(f"Failed to count chunks for {codebase_name} in Weaviate: {e}")
            return 0
    
    def get_codebase_metadata(self, codebase_name: str) -> Dict:
        """Get metadata about a codebase from Weaviate"""
        try:
            # Get sample chunks to analyze
            chunks = self.get_all_chunks_for_codebase(codebase_name, limit=100)
            
            languages = set()
            file_types = set()
            
            for chunk in chunks:
                metadata = chunk.get('metadata', {})
                if metadata.get('language'):
                    languages.add(metadata['language'])
                if metadata.get('file_path'):
                    file_ext = metadata['file_path'].split('.')[-1] if '.' in metadata['file_path'] else ''
                    if file_ext:
                        file_types.add(file_ext)
            
            return {
                'name': codebase_name,
                'total_chunks': self.count_chunks_for_codebase(codebase_name),
                'languages': list(languages),
                'file_types': list(file_types),
                'status': 'ready'
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get metadata for {codebase_name} from Weaviate: {e}")
            return {'name': codebase_name, 'status': 'error', 'error': str(e)}

def create_vector_db_interface(db_type: str, **kwargs) -> VectorDBInterface:
    """Factory function to create appropriate vector DB interface"""
    
    if db_type.lower() == 'chromadb':
        return ChromaDBInterface(kwargs['client'], kwargs.get('logger'))
    elif db_type.lower() == 'pinecone':
        return PineconeInterface(kwargs['client'], kwargs['index_name'], kwargs.get('logger'))
    elif db_type.lower() == 'weaviate':
        return WeaviateInterface(kwargs['client'], kwargs['class_name'], kwargs.get('logger'))
    else:
        raise ValueError(f"Unsupported vector database type: {db_type}")

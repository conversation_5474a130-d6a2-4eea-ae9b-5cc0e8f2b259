{".class": "MypyFile", "_fullname": "open_webui_code_analyzer_tool", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "pydantic.main.BaseModel", "kind": "Gdef"}, "CacheConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.CacheConfig", "name": "CacheConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 29, "name": "memory_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 30, "name": "memory_ttl", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 31, "name": "disk_size", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 32, "name": "disk_ttl", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 33, "name": "disk_path", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 34, "name": "enable_compression", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 35, "name": "cache_hit_extend_ttl", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.CacheConfig", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "memory_size", "memory_ttl", "disk_size", "disk_ttl", "disk_path", "enable_compression", "cache_hit_extend_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheConfig.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "memory_size", "memory_ttl", "disk_size", "disk_ttl", "disk_path", "enable_compression", "cache_hit_extend_ttl"], "arg_types": ["open_webui_code_analyzer_tool.CacheConfig", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CacheConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "memory_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "memory_ttl"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disk_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disk_ttl"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disk_path"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "enable_compression"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cache_hit_extend_ttl"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["memory_size", "memory_ttl", "disk_size", "disk_ttl", "disk_path", "enable_compression", "cache_hit_extend_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["memory_size", "memory_ttl", "disk_size", "disk_ttl", "disk_path", "enable_compression", "cache_hit_extend_ttl"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CacheConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["memory_size", "memory_ttl", "disk_size", "disk_ttl", "disk_path", "enable_compression", "cache_hit_extend_ttl"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.bool", "builtins.bool"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CacheConfig", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "cache_hit_extend_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.cache_hit_extend_ttl", "name": "cache_hit_extend_ttl", "type": "builtins.bool"}}, "disk_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.disk_path", "name": "disk_path", "type": "builtins.str"}}, "disk_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.disk_size", "name": "disk_size", "type": "builtins.int"}}, "disk_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.disk_ttl", "name": "disk_ttl", "type": "builtins.int"}}, "enable_compression": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.enable_compression", "name": "enable_compression", "type": "builtins.bool"}}, "memory_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.memory_size", "name": "memory_size", "type": "builtins.int"}}, "memory_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheConfig.memory_ttl", "name": "memory_ttl", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.CacheConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.CacheConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CacheEntry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.CacheEntry", "name": "Cache<PERSON>ntry", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheEntry", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.CacheEntry", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "ttl", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheEntry.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "data", "ttl", "tags"], "arg_types": ["open_webui_code_analyzer_tool.CacheEntry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CacheEntry", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "extend_ttl", "ttl_extension"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheEntry.access", "name": "access", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "extend_ttl", "ttl_extension"], "arg_types": ["open_webui_code_analyzer_tool.CacheEntry", "builtins.bool", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "access of CacheEntry", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "access_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheEntry.access_count", "name": "access_count", "type": "builtins.int"}}, "created_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheEntry.created_at", "name": "created_at", "type": "builtins.float"}}, "data": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheEntry.data", "name": "data", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "expires_at": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheEntry.expires_at", "name": "expires_at", "type": "builtins.float"}}, "is_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheEntry.is_expired", "name": "is_expired", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.CacheEntry"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_expired of CacheEntry", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last_accessed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheEntry.last_accessed", "name": "last_accessed", "type": "builtins.float"}}, "size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheEntry.size", "name": "size", "type": "builtins.int"}}, "tags": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheEntry.tags", "name": "tags", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.CacheEntry.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.CacheEntry", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CacheStats": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.CacheStats", "name": "CacheStats", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheStats", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 41, "name": "memory_hits", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 42, "name": "memory_misses", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 43, "name": "disk_hits", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 44, "name": "disk_misses", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 45, "name": "total_requests", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.CacheStats", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "open_webui_code_analyzer_tool.CacheStats.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "memory_hits", "memory_misses", "disk_hits", "disk_misses", "total_requests"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.CacheStats.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1], "arg_names": ["self", "memory_hits", "memory_misses", "disk_hits", "disk_misses", "total_requests"], "arg_types": ["open_webui_code_analyzer_tool.CacheStats", "builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CacheStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "open_webui_code_analyzer_tool.CacheStats.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "memory_hits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "memory_misses"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disk_hits"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disk_misses"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "total_requests"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["memory_hits", "memory_misses", "disk_hits", "disk_misses", "total_requests"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "open_webui_code_analyzer_tool.CacheStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["memory_hits", "memory_misses", "disk_hits", "disk_misses", "total_requests"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CacheStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "open_webui_code_analyzer_tool.CacheStats.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5], "arg_names": ["memory_hits", "memory_misses", "disk_hits", "disk_misses", "total_requests"], "arg_types": ["builtins.int", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of CacheStats", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "disk_hits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheStats.disk_hits", "name": "disk_hits", "type": "builtins.int"}}, "disk_misses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheStats.disk_misses", "name": "disk_misses", "type": "builtins.int"}}, "hit_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "open_webui_code_analyzer_tool.CacheStats.hit_rate", "name": "hit_rate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hit_rate of CacheStats", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheStats.hit_rate", "name": "hit_rate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "hit_rate of CacheStats", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "memory_hit_rate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "open_webui_code_analyzer_tool.CacheStats.memory_hit_rate", "name": "memory_hit_rate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "memory_hit_rate of CacheStats", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "open_webui_code_analyzer_tool.CacheStats.memory_hit_rate", "name": "memory_hit_rate", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.CacheStats"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "memory_hit_rate of CacheStats", "ret_type": "builtins.float", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "memory_hits": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheStats.memory_hits", "name": "memory_hits", "type": "builtins.int"}}, "memory_misses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheStats.memory_misses", "name": "memory_misses", "type": "builtins.int"}}, "total_requests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.CacheStats.total_requests", "name": "total_requests", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.CacheStats.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.CacheStats", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DiskCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.DiskCache", "name": "<PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.DiskCache", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cache_dir", "max_size", "default_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "cache_dir", "max_size", "default_ttl"], "arg_types": ["open_webui_code_analyzer_tool.DiskCache", "builtins.str", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DiskCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evict_if_needed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache._evict_if_needed", "name": "_evict_if_needed", "type": null}}, "_get_file_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache._get_file_path", "name": "_get_file_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["open_webui_code_analyzer_tool.DiskCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_file_path of DiskCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_load_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache._load_index", "name": "_load_index", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.DiskCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_load_index of DiskCache", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_remove_entry": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache._remove_entry", "name": "_remove_entry", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["open_webui_code_analyzer_tool.DiskCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_remove_entry of DiskCache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_save_index": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache._save_index", "name": "_save_index", "type": null}}, "cache_dir": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.DiskCache.cache_dir", "name": "cache_dir", "type": "builtins.str"}}, "clear_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache.clear_expired", "name": "clear_expired", "type": null}}, "default_ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.DiskCache.default_ttl", "name": "default_ttl", "type": "builtins.int"}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["open_webui_code_analyzer_tool.DiskCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of DiskCache", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache.get_stats", "name": "get_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.DiskCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stats of DiskCache", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.DiskCache.index", "name": "index", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "index_file": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.DiskCache.index_file", "name": "index_file", "type": "builtins.str"}}, "lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.DiskCache.lock", "name": "lock", "type": "_thread.RLock"}}, "max_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.DiskCache.max_size", "name": "max_size", "type": "builtins.int"}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "key", "data", "ttl", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.DiskCache.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "key", "data", "ttl", "tags"], "arg_types": ["open_webui_code_analyzer_tool.DiskCache", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put of DiskCache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.DiskCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.DiskCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Field": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef"}, "IntelligentCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.IntelligentCache", "name": "IntelligentCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.IntelligentCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.IntelligentCache", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "config"], "arg_types": ["open_webui_code_analyzer_tool.IntelligentCache", {".class": "UnionType", "items": ["open_webui_code_analyzer_tool.CacheConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IntelligentCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_start_cleanup_thread": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.IntelligentCache._start_cleanup_thread", "name": "_start_cleanup_thread", "type": null}}, "cache_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "query", "codebase", "context", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.cache_context", "name": "cache_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "query", "codebase", "context", "filters"], "arg_types": ["open_webui_code_analyzer_tool.IntelligentCache", "builtins.str", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "cache_context of IntelligentCache", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.config", "name": "config", "type": "open_webui_code_analyzer_tool.CacheConfig"}}, "disk_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.disk_cache", "name": "disk_cache", "type": "open_webui_code_analyzer_tool.DiskCache"}}, "generate_cache_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "query", "codebase", "filters", "context_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.generate_cache_key", "name": "generate_cache_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "query", "codebase", "filters", "context_type"], "arg_types": ["open_webui_code_analyzer_tool.IntelligentCache", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_cache_key of IntelligentCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cache_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.get_cache_stats", "name": "get_cache_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.IntelligentCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cache_stats of IntelligentCache", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cached_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "query", "codebase", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.get_cached_context", "name": "get_cached_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "query", "codebase", "filters"], "arg_types": ["open_webui_code_analyzer_tool.IntelligentCache", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cached_context of IntelligentCache", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "invalidate_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "codebase"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.invalidate_codebase", "name": "invalidate_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "codebase"], "arg_types": ["open_webui_code_analyzer_tool.IntelligentCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "invalidate_codebase of IntelligentCache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "memory_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.memory_cache", "name": "memory_cache", "type": "open_webui_code_analyzer_tool.MemoryCache"}}, "stats": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.IntelligentCache.stats", "name": "stats", "type": "open_webui_code_analyzer_tool.CacheStats"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.IntelligentCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.IntelligentCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MemoryCache": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.MemoryCache", "name": "MemoryCache", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.MemoryCache", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "max_size", "default_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "max_size", "default_ttl"], "arg_types": ["open_webui_code_analyzer_tool.MemoryCache", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of MemoryCache", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_generate_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "query", "codebase", "filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache._generate_key", "name": "_generate_key", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "query", "codebase", "filters"], "arg_types": ["open_webui_code_analyzer_tool.MemoryCache", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_generate_key of MemoryCache", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "open_webui_code_analyzer_tool.MemoryCache.cache", "name": "cache", "type": {".class": "Instance", "args": ["builtins.str", "open_webui_code_analyzer_tool.CacheEntry"], "extra_attrs": null, "type_ref": "collections.OrderedDict"}}}, "clear_by_tags": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache.clear_by_tags", "name": "clear_by_tags", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tags"], "arg_types": ["open_webui_code_analyzer_tool.MemoryCache", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_by_tags of MemoryCache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "clear_expired": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache.clear_expired", "name": "clear_expired", "type": null}}, "default_ttl": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.MemoryCache.default_ttl", "name": "default_ttl", "type": "builtins.int"}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "key"], "arg_types": ["open_webui_code_analyzer_tool.MemoryCache", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get of MemoryCache", "ret_type": {".class": "UnionType", "items": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache.get_stats", "name": "get_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.MemoryCache"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stats of MemoryCache", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "lock": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.MemoryCache.lock", "name": "lock", "type": "_thread.RLock"}}, "max_size": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.MemoryCache.max_size", "name": "max_size", "type": "builtins.int"}}, "put": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "key", "data", "ttl", "tags"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.MemoryCache.put", "name": "put", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "key", "data", "ttl", "tags"], "arg_types": ["open_webui_code_analyzer_tool.MemoryCache", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "put of MemoryCache", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.MemoryCache.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.MemoryCache", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OrderedDict": {".class": "SymbolTableNode", "cross_ref": "collections.OrderedDict", "kind": "Gdef"}, "Tools": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.Tools", "name": "Tools", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.Tools", "builtins.object"], "names": {".class": "SymbolTable", "Valves": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["pydantic.main.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "open_webui_code_analyzer_tool.Tools.Valves", "name": "Valves", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools.Valves", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 175, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 178, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 181, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 394, "name": "code_analyzer_server_url", "type": "builtins.str"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 398, "name": "request_timeout", "type": "builtins.int"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 402, "name": "ai_timeout", "type": "builtins.int"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 406, "name": "max_results", "type": "builtins.int"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 410, "name": "auto_context_injection", "type": "builtins.bool"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 414, "name": "current_codebase", "type": "builtins.str"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 418, "name": "context_format", "type": "builtins.str"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 424, "name": "enable_caching", "type": "builtins.bool"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 428, "name": "cache_memory_size", "type": "builtins.int"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 432, "name": "cache_memory_ttl", "type": "builtins.int"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 436, "name": "cache_disk_size", "type": "builtins.int"}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 440, "name": "cache_disk_ttl", "type": "builtins.int"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "open_webui_code_analyzer_tool", "mro": ["open_webui_code_analyzer_tool.Tools.Valves", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "code_analyzer_server_url", "request_timeout", "ai_timeout", "max_results", "auto_context_injection", "current_codebase", "context_format", "enable_caching", "cache_memory_size", "cache_memory_ttl", "cache_disk_size", "cache_disk_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "code_analyzer_server_url", "request_timeout", "ai_timeout", "max_results", "auto_context_injection", "current_codebase", "context_format", "enable_caching", "cache_memory_size", "cache_memory_ttl", "cache_disk_size", "cache_disk_ttl"], "arg_types": ["open_webui_code_analyzer_tool.Tools.Valves", "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of Valves", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "code_analyzer_server_url"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "request_timeout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "ai_timeout"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "max_results"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "auto_context_injection"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "current_codebase"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "context_format"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "enable_caching"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cache_memory_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cache_memory_ttl"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cache_disk_size"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cache_disk_ttl"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "code_analyzer_server_url", "request_timeout", "ai_timeout", "max_results", "auto_context_injection", "current_codebase", "context_format", "enable_caching", "cache_memory_size", "cache_memory_ttl", "cache_disk_size", "cache_disk_ttl"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "code_analyzer_server_url", "request_timeout", "ai_timeout", "max_results", "auto_context_injection", "current_codebase", "context_format", "enable_caching", "cache_memory_size", "cache_memory_ttl", "cache_disk_size", "cache_disk_ttl"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "code_analyzer_server_url", "request_timeout", "ai_timeout", "max_results", "auto_context_injection", "current_codebase", "context_format", "enable_caching", "cache_memory_size", "cache_memory_ttl", "cache_disk_size", "cache_disk_ttl"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", "builtins.int", "builtins.int", "builtins.bool", "builtins.str", "builtins.str", "builtins.bool", "builtins.int", "builtins.int", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of <PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "ai_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.ai_timeout", "name": "ai_timeout", "type": "builtins.int"}}, "auto_context_injection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.auto_context_injection", "name": "auto_context_injection", "type": "builtins.bool"}}, "cache_disk_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.cache_disk_size", "name": "cache_disk_size", "type": "builtins.int"}}, "cache_disk_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.cache_disk_ttl", "name": "cache_disk_ttl", "type": "builtins.int"}}, "cache_memory_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.cache_memory_size", "name": "cache_memory_size", "type": "builtins.int"}}, "cache_memory_ttl": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.cache_memory_ttl", "name": "cache_memory_ttl", "type": "builtins.int"}}, "code_analyzer_server_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.code_analyzer_server_url", "name": "code_analyzer_server_url", "type": "builtins.str"}}, "context_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.context_format", "name": "context_format", "type": "builtins.str"}}, "current_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.current_codebase", "name": "current_codebase", "type": "builtins.str"}}, "enable_caching": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.enable_caching", "name": "enable_caching", "type": "builtins.bool"}}, "max_results": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.max_results", "name": "max_results", "type": "builtins.int"}}, "request_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "open_webui_code_analyzer_tool.Tools.Valves.request_timeout", "name": "request_timeout", "type": "builtins.int"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.Tools.Valves.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.Tools.Valves", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 4], "arg_names": ["self", "user_query", "_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 4], "arg_names": ["self", "user_query", "_kwargs"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__call__ of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__dir__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools.__dir__", "name": "__dir__", "type": null}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools.__getattr__", "name": "__getattr__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools.__init__", "name": "__init__", "type": null}}, "_add_chunk_clarification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stats_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._add_chunk_clarification", "name": "_add_chunk_clarification", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stats_text"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_add_chunk_clarification of Tools", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_analyze_query_for_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._analyze_query_for_context", "name": "_analyze_query_for_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_analyze_query_for_context of Tools", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_auto_inject_optimized_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "user_query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools._auto_inject_optimized_context", "name": "_auto_inject_optimized_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "user_query"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_auto_inject_optimized_context of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_optimal_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.Tools._current_optimal_format", "name": "_current_optimal_format", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "_detect_code_related_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._detect_code_related_query", "name": "_detect_code_related_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_code_related_query of Tools", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_detect_query_intent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._detect_query_intent", "name": "_detect_query_intent", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_detect_query_intent of Tools", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_optimal_context_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query_lower"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._determine_optimal_context_format", "name": "_determine_optimal_context_format", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query_lower"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_optimal_context_format of Tools", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ensure_chunk_clarification": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response_text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._ensure_chunk_clarification", "name": "_ensure_chunk_clarification", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response_text"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_chunk_clarification of Tools", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_ensure_codebase_selected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "target_codebase"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools._ensure_codebase_selected", "name": "_ensure_codebase_selected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "target_codebase"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_ensure_codebase_selected of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_explain_format_choice": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "query_lower", "chosen_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._explain_format_choice", "name": "_explain_format_choice", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "query_lower", "chosen_format"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_explain_format_choice of Tools", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_context_for_openwebui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "search_result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._format_context_for_openwebui", "name": "_format_context_for_openwebui", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "search_result"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_context_for_openwebui of Tools", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_available_codebase_names": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools._get_available_codebase_names", "name": "_get_available_codebase_names", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.Tools"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_available_codebase_names of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_handle_management_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools._handle_management_query", "name": "_handle_management_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_handle_management_query of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_code_related_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "query"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "open_webui_code_analyzer_tool.Tools._is_code_related_query", "name": "_is_code_related_query", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "query"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_code_related_query of Tools", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_private_methods": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.Tools._private_methods", "name": "_private_methods", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_suggest_codebase_selection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools._suggest_codebase_selection", "name": "_suggest_codebase_selection", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.Tools"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_suggest_codebase_selection of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_sync_with_server_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools._sync_with_server_state", "name": "_sync_with_server_state", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["open_webui_code_analyzer_tool.Tools"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_sync_with_server_state of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.bool"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "ask_about_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "question", "codebase_name", "n_results", "filter_type", "filter_language", "filter_file", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.ask_about_code", "name": "ask_about_code", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "question", "codebase_name", "n_results", "filter_type", "filter_language", "filter_file", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "ask_about_code of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.Tools.cache", "name": "cache", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "check_system_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "detailed", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.check_system_status", "name": "check_system_status", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "detailed", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.bool", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_system_status of <PERSON>ls", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "citation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.Tools.citation", "name": "citation", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "clear_cache": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "cache_type", "codebase_name", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.clear_cache", "name": "clear_cache", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1], "arg_names": ["self", "cache_type", "codebase_name", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "clear_cache of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "codebase_name", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.delete_codebase", "name": "delete_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "codebase_name", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "delete_codebase of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_cache_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.get_cache_stats", "name": "get_cache_stats", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_cache_stats of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_code_analyzer_help": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.get_code_analyzer_help", "name": "get_code_analyzer_help", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_code_analyzer_help of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_code_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "query", "codebase_name", "n_results", "filter_type", "filter_language", "filter_file", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.get_code_context", "name": "get_code_context", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "query", "codebase_name", "n_results", "filter_type", "filter_language", "filter_file", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_code_context of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_codebase_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "codebase_name", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.get_codebase_stats", "name": "get_codebase_stats", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "codebase_name", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_codebase_stats of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_detailed_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.get_detailed_status", "name": "get_detailed_status", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_detailed_status of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_server_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.get_server_status", "name": "get_server_status", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_server_status of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "inject_context_for_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "user_query", "codebase_name", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.inject_context_for_query", "name": "inject_context_for_query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "user_query", "codebase_name", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "inject_context_for_query of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_codebases": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.list_codebases", "name": "list_codebases", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "list_codebases of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "codebase_name", "exclude_dirs", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.process_codebase", "name": "process_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "codebase_name", "exclude_dirs", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_codebase of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raw_search": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "codebase_name", "n_results", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.raw_search", "name": "raw_search", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "query", "codebase_name", "n_results", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raw_search of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "search_code": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "query", "codebase_name", "n_results", "filter_type", "filter_language", "filter_file", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.search_code", "name": "search_code", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1, 1, 1, 1], "arg_names": ["self", "query", "codebase_name", "n_results", "filter_type", "filter_language", "filter_file", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", "builtins.str", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "search_code of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "select_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "codebase_name", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.select_codebase", "name": "select_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "codebase_name", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_codebase of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_context_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "format_type", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.set_context_format", "name": "set_context_format", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "format_type", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_context_format of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "smart_code_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "query", "codebase_name", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.smart_code_context", "name": "smart_code_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "query", "codebase_name", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "smart_code_context of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "test_format_selection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "test_query", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.test_format_selection", "name": "test_format_selection", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "test_query", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", "builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "test_format_selection of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "toggle_auto_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "open_webui_code_analyzer_tool.Tools.toggle_auto_context", "name": "toggle_auto_context", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "__event_emitter__"], "arg_types": ["open_webui_code_analyzer_tool.Tools", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "toggle_auto_context of Tools", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "valves": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "open_webui_code_analyzer_tool.Tools.valves", "name": "valves", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "open_webui_code_analyzer_tool.Tools.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "open_webui_code_analyzer_tool.Tools", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "open_webui_code_analyzer_tool.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "open_webui_code_analyzer_tool.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "open_webui_code_analyzer_tool.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "open_webui_code_analyzer_tool.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "open_webui_code_analyzer_tool.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "open_webui_code_analyzer_tool.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "field": {".class": "SymbolTableNode", "cross_ref": "dataclasses.field", "kind": "Gdef"}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "pickle": {".class": "SymbolTableNode", "cross_ref": "pickle", "kind": "Gdef"}, "requests": {".class": "SymbolTableNode", "cross_ref": "requests", "kind": "Gdef"}, "threading": {".class": "SymbolTableNode", "cross_ref": "threading", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\open_webui_code_analyzer_tool.py"}
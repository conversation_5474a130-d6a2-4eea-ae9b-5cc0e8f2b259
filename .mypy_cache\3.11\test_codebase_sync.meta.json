{"data_mtime": 1751379603, "dep_lines": [6, 7, 8, 9, 14, 55, 119, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 20, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "json", "sys", "pathlib", "open_webui_code_analyzer_tool", "re", "asyncio", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "http", "http.cookiejar", "os", "requests.auth", "requests.models", "typing", "typing_extensions"], "hash": "fc434c46a23c586afe425dae357a5399ceb52ae3", "id": "test_codebase_sync", "ignore_all": false, "interface_hash": "c830ac3e587d1e4ea595125e85ab405635d8ecda", "mtime": 1751379937, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_codebase_sync.py", "plugin_data": null, "size": 5213, "suppressed": [], "version_id": "1.15.0"}
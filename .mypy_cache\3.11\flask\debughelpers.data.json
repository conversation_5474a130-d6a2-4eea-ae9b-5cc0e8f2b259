{".class": "MypyFile", "_fullname": "flask.debughelpers", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "App": {".class": "SymbolTableNode", "cross_ref": "flask.sansio.app.App", "kind": "Gdef"}, "BaseLoader": {".class": "SymbolTableNode", "cross_ref": "jinja2.loaders.BaseLoader", "kind": "Gdef"}, "Blueprint": {".class": "SymbolTableNode", "cross_ref": "flask.blueprints.Blueprint", "kind": "Gdef"}, "DebugFilesKeyError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.KeyError", "builtins.AssertionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask.debughelpers.DebugFilesKeyError", "name": "DebugFilesKeyError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask.debughelpers.DebugFilesKeyError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask.debughelpers", "mro": ["flask.debughelpers.DebugFilesKeyError", "builtins.KeyError", "builtins.LookupError", "builtins.AssertionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "key"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.debughelpers.DebugFilesKeyError.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "key"], "arg_types": ["flask.debughelpers.DebugFilesKeyError", "flask.wrappers.Request", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of DebugFilesKeyError", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__str__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.debughelpers.DebugFilesKeyError.__str__", "name": "__str__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["flask.debughelpers.DebugFilesKeyError"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__str__ of DebugFilesKeyError", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "msg": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "flask.debughelpers.DebugFilesKeyError.msg", "name": "msg", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask.debughelpers.DebugFilesKeyError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask.debughelpers.DebugFilesKeyError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FormDataRoutingRedirect": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.AssertionError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask.debughelpers.FormDataRoutingRedirect", "name": "FormDataRoutingRedirect", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask.debughelpers.FormDataRoutingRedirect", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask.debughelpers", "mro": ["flask.debughelpers.FormDataRoutingRedirect", "builtins.AssertionError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.debughelpers.FormDataRoutingRedirect.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "request"], "arg_types": ["flask.debughelpers.FormDataRoutingRedirect", "flask.wrappers.Request"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of FormDataRoutingRedirect", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask.debughelpers.FormDataRoutingRedirect.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask.debughelpers.FormDataRoutingRedirect", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Request": {".class": "SymbolTableNode", "cross_ref": "flask.wrappers.Request", "kind": "Gdef"}, "RequestRedirect": {".class": "SymbolTableNode", "cross_ref": "werkzeug.routing.exceptions.RequestRedirect", "kind": "Gdef"}, "Scaffold": {".class": "SymbolTableNode", "cross_ref": "flask.sansio.scaffold.Scaffold", "kind": "Gdef"}, "UnexpectedUnicodeError": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.AssertionError", "builtins.UnicodeError"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "flask.debughelpers.UnexpectedUnicodeError", "name": "UnexpectedUnicodeError", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "flask.debughelpers.UnexpectedUnicodeError", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "flask.debughelpers", "mro": ["flask.debughelpers.UnexpectedUnicodeError", "builtins.AssertionError", "builtins.UnicodeError", "builtins.ValueError", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "flask.debughelpers.UnexpectedUnicodeError.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "flask.debughelpers.UnexpectedUnicodeError", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.debughelpers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.debughelpers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.debughelpers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.debughelpers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.debughelpers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "flask.debughelpers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_dump_loader_info": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["loader"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.debughelpers._dump_loader_info", "name": "_dump_loader_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["loader"], "arg_types": ["jinja2.loaders.BaseLoader"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_dump_loader_info", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "attach_enctype_error_multidict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["request"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.debughelpers.attach_enctype_error_multidict", "name": "attach_enctype_error_multidict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["request"], "arg_types": ["flask.wrappers.Request"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "attach_enctype_error_multidict", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "explain_template_loading_attempts": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["app", "template", "attempts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "flask.debughelpers.explain_template_loading_attempts", "name": "explain_template_loading_attempts", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["app", "template", "attempts"], "arg_types": ["flask.sansio.app.App", "builtins.str", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["jinja2.loaders.BaseLoader", "flask.sansio.scaffold.Scaffold", {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "explain_template_loading_attempts", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "request_ctx": {".class": "SymbolTableNode", "cross_ref": "flask.globals.request_ctx", "kind": "Gdef"}, "t": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\debughelpers.py"}
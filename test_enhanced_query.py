#!/usr/bin/env python3
"""
Test what the enhanced query actually looks like.
"""

import requests
import json

def test_enhanced_query():
    """Test the enhanced query directly"""
    print("🔍 Testing Enhanced Query")
    print("=" * 50)
    
    server_url = "http://home-ai-server.local:5002"
    
    # Test the enhanced query
    enhanced_query = "how is memory managed in the utils codebase? tmwmem malloc free allocation deallocation buffer pool"
    
    payload = {
        "query": enhanced_query,
        "codebase_name": "utils",
        "n_results": 20,
        "context_preferences": None
    }
    
    print("1. Testing enhanced query...")
    print(f"   Enhanced query: {enhanced_query}")
    print(f"   n_results: {payload['n_results']}")
    
    try:
        response = requests.post(
            f"{server_url}/tools/get_optimized_context",
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            result = data.get("result", "")
            
            import re
            found_match = re.search(r'Found (\d+) relevant code section', result)
            context_matches = re.findall(r'## Context \d+', result)
            
            print(f"   Result length: {len(result)}")
            print(f"   'Found X relevant': {found_match.group(1) if found_match else 'None'}")
            print(f"   '## Context X' matches: {len(context_matches)}")
            
            if found_match and int(found_match.group(1)) > 1:
                print(f"   🎉 SUCCESS: Enhanced query found {found_match.group(1)} chunks!")
            else:
                print(f"   ⚠️ Enhanced query still only finding limited results")
                
                # Try a more targeted approach
                print("\n2. Testing more targeted queries...")
                targeted_queries = [
                    "tmwmem",
                    "malloc free",
                    "memory allocation buffer",
                    "tmwmem_lowFree tmwmem_alloc"
                ]
                
                for target_query in targeted_queries:
                    payload["query"] = target_query
                    payload["n_results"] = 15
                    
                    try:
                        response = requests.post(
                            f"{server_url}/tools/get_optimized_context",
                            json=payload,
                            headers={"Content-Type": "application/json"},
                            timeout=30
                        )
                        
                        if response.status_code == 200:
                            data = response.json()
                            result = data.get("result", "")
                            
                            found_match = re.search(r'Found (\d+) relevant code section', result)
                            print(f"   Query '{target_query}': {found_match.group(1) if found_match else '0'} chunks")
                            
                            if found_match and int(found_match.group(1)) > 1:
                                print(f"     ✅ This query works better!")
                                break
                    except Exception as e:
                        print(f"     ❌ Error with '{target_query}': {e}")
        else:
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"   Exception: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Enhanced query test completed!")

if __name__ == "__main__":
    test_enhanced_query()

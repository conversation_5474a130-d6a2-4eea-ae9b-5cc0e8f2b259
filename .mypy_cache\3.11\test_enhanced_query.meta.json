{"data_mtime": 1751391190, "dep_lines": [6, 7, 44, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["requests", "json", "re", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "http", "http.cookiejar", "requests.auth", "requests.models", "typing", "typing_extensions"], "hash": "a0f4c44988d94da26b2c4ef8ae8288fbbcd0afd9", "id": "test_enhanced_query", "ignore_all": false, "interface_hash": "08ae1f7f037ad5c318df261916c55e8aa8713e35", "mtime": 1751391189, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_enhanced_query.py", "plugin_data": null, "size": 3779, "suppressed": [], "version_id": "1.15.0"}
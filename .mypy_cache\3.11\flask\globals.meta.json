{"data_mtime": 1751398266, "dep_lines": [6, 9, 10, 13, 14, 1, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 25, 25, 25, 25, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["werkzeug.local", "flask.app", "flask.ctx", "flask.sessions", "flask.wrappers", "__future__", "typing", "<PERSON><PERSON><PERSON>", "builtins", "_contextvars", "_frozen_importlib", "abc", "flask.sansio", "flask.sansio.app", "flask.sansio.scaffold", "werkzeug", "werkzeug.sansio", "werkzeug.sansio.request", "werkzeug.wrappers", "werkzeug.wrappers.request"], "hash": "a0c723e6e39cf9cfb7ec1ee87a9fa46099d8bc72", "id": "flask.globals", "ignore_all": true, "interface_hash": "5687a5667dc1cce9831f2dd590d48da4dee31368", "mtime": 1708667823, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\AppData\\Local\\Packages\\PythonSoftwareFoundation.Python.3.11_qbz5n2kfra8p0\\LocalCache\\local-packages\\Python311\\site-packages\\flask\\globals.py", "plugin_data": null, "size": 1713, "suppressed": [], "version_id": "1.15.0"}